"use client"

import { useState } from 'react'
import { Plus, Trash2, <PERSON>ting<PERSON>, Type, ChevronDown, GripVertical, X, Edit3, Eye } from 'lucide-react'
import type { CustomField } from '../../../types'

interface CustomFieldFormData {
  id?: string
  label: string
  field_type: 'text' | 'dropdown'
  required: boolean
  placeholder: string
  description: string
  field_order: number
  options: (string | { label: string; value: string })[]
}

interface CustomFieldsTabProps {
  customFields: CustomFieldFormData[] | null
  onChange: (fields: CustomFieldFormData[]) => void
  errors: Record<string, any>
  loading?: boolean
}

export default function CustomFieldsTab({
  customFields,
  onChange,
  errors,
  loading = false
}: CustomFieldsTabProps) {
  // Ensure customFields is always an array
  const safeCustomFields = customFields || []
  const [optionInputs, setOptionInputs] = useState<Record<number, string>>({})
  const [showModal, setShowModal] = useState(false)
  const [editingField, setEditingField] = useState<CustomFieldFormData | null>(null)
  const [modalFormData, setModalFormData] = useState<CustomFieldFormData>({
    label: '',
    field_type: 'text',
    required: false,
    placeholder: '',
    description: '',
    field_order: 0,
    options: []
  })
  const [modalNewOption, setModalNewOption] = useState('')
  const [modalErrors, setModalErrors] = useState<Record<string, string>>({})
  
  const openCreateModal = () => {
    setEditingField(null)
    setModalFormData({
      label: '',
      field_type: 'text',
      required: false,
      placeholder: '',
      description: '',
      field_order: customFields?.length || 0,
      options: []
    })
    setModalNewOption('')
    setModalErrors({})
    setShowModal(true)
  }

  const openEditModal = (index: number) => {
    const field = customFields?.[index]
    if (!field) return
    setEditingField({ ...field, field_order: index })
    setModalFormData({ ...field, field_order: index })
    setModalNewOption('')
    setModalErrors({})
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingField(null)
    setModalFormData({
      label: '',
      field_type: 'text',
      required: false,
      placeholder: '',
      description: '',
      field_order: 0,
      options: []
    })
    setModalNewOption('')
    setModalErrors({})
  }

  const removeCustomField = (index: number) => {
    const newFields = safeCustomFields.filter((_, i) => i !== index)
    // Reorder remaining fields
    const reorderedFields = newFields.map((field, i) => ({ ...field, field_order: i }))
    onChange(reorderedFields)
  }

  const updateCustomField = (index: number, updates: Partial<CustomFieldFormData>) => {
    const newFields = safeCustomFields.map((field, i) =>
      i === index ? { ...field, ...updates } : field
    )
    onChange(newFields)
  }

  const moveField = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === safeCustomFields.length - 1)
    ) {
      return
    }

    const newFields = [...safeCustomFields]
    const targetIndex = direction === 'up' ? index - 1 : index + 1

    // Swap fields
    ;[newFields[index], newFields[targetIndex]] = [newFields[targetIndex], newFields[index]]

    // Update field orders
    const reorderedFields = newFields.map((field, i) => ({ ...field, field_order: i }))
    onChange(reorderedFields)
  }

  const addOption = (fieldIndex: number, option: string) => {
    if (!option.trim()) return

    const field = safeCustomFields[fieldIndex]
    if (!field || !field.options) return

    // Check if option already exists (handle both string and object formats)
    const optionExists = field.options.some(existingOption => {
      const optionValue = typeof existingOption === 'string' ? existingOption : (existingOption.label || existingOption.value)
      return optionValue === option.trim()
    })

    if (optionExists) return

    updateCustomField(fieldIndex, {
      options: [...field.options, option.trim()]
    })
  }

  const removeOption = (fieldIndex: number, optionIndex: number) => {
    const field = safeCustomFields[fieldIndex]
    if (!field || !field.options) return

    const newOptions = field.options.filter((_, i) => i !== optionIndex)
    updateCustomField(fieldIndex, { options: newOptions })
  }

  const updateModalFormData = (updates: Partial<CustomFieldFormData>) => {
    setModalFormData(prev => ({ ...prev, ...updates }))
    // Clear related errors
    const newErrors = { ...modalErrors }
    Object.keys(updates).forEach(key => {
      delete newErrors[key]
    })
    setModalErrors(newErrors)
  }

  const addModalOption = () => {
    if (!modalNewOption.trim()) return

    const safeOptions = modalFormData.options || []

    // Check if option already exists (handle both string and object formats)
    const optionExists = safeOptions.some(option => {
      const optionValue = typeof option === 'string' ? option : (option.label || option.value)
      return optionValue === modalNewOption.trim()
    })

    if (optionExists) {
      setModalErrors({ options: 'هذا الخيار موجود بالفعل' })
      return
    }

    // Add option as string (will be normalized later when saving)
    updateModalFormData({
      options: [...safeOptions, modalNewOption.trim()]
    })
    setModalNewOption('')
  }

  const removeModalOption = (index: number) => {
    const safeOptions = modalFormData.options || []
    const newOptions = safeOptions.filter((_, i) => i !== index)
    updateModalFormData({ options: newOptions })
  }

  const validateModalForm = () => {
    const newErrors: Record<string, string> = {}

    if (!modalFormData.label.trim()) {
      newErrors.label = 'تسمية الحقل مطلوبة'
    }

    if (modalFormData.field_type === 'dropdown' && (!modalFormData.options || modalFormData.options.length === 0)) {
      newErrors.options = 'يجب إضافة خيار واحد على الأقل للقائمة المنسدلة'
    }

    setModalErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const saveModalField = () => {
    if (!validateModalForm()) return

    if (editingField !== null) {
      // Edit existing field
      const newFields = safeCustomFields.map((field, i) =>
        i === editingField.field_order ? modalFormData : field
      )
      onChange(newFields)
    } else {
      // Add new field
      onChange([...safeCustomFields, modalFormData])
    }

    closeModal()
  }

  const getFieldErrors = (index: number) => {
    return errors.customFields?.[index] || {}
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium flex items-center gap-2">
            <Settings className="w-5 h-5" />
            الحقول المخصصة
          </h3>
          <p className="text-gray-400 text-sm mt-1">
            أضف حقول إضافية لجمع معلومات مخصصة من العملاء
          </p>
        </div>
        <button
          type="button"
          onClick={openCreateModal}
          disabled={loading}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          <Plus className="w-4 h-4" />
          إضافة حقل
        </button>
      </div>

      {/* Custom Fields List */}
      {safeCustomFields.length === 0 ? (
        <div className="text-center py-12 border-2 border-dashed border-gray-600 rounded-lg">
          <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-300 mb-2">
            لا توجد حقول مخصصة
          </h3>
          <p className="text-gray-400 mb-4">
            أضف حقول مخصصة لجمع معلومات إضافية من العملاء عند الطلب
          </p>
          <button
            type="button"
            onClick={openCreateModal}
            disabled={loading}
            className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            <Plus className="w-4 h-4" />
            إضافة حقل
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {safeCustomFields.map((field, index) => (
            <div key={index} className="bg-gray-800/30 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors">
              {/* Field Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center">
                    <Type className="w-5 h-5 text-purple-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-white">
                      {field.label || `الحقل ${index + 1}`}
                    </h4>
                    <p className="text-sm text-gray-400">
                      {(field.field_type === 'text' && (!field.options || field.options.length === 0)) ? 'نص' : 'قائمة منسدلة'}
                      {field.required && ' • مطلوب'}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-1">
                  <button
                    type="button"
                    onClick={() => moveField(index, 'up')}
                    disabled={index === 0 || loading}
                    className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    title="تحريك لأعلى"
                  >
                    <GripVertical className="w-4 h-4" />
                  </button>
                  <button
                    type="button"
                    onClick={() => moveField(index, 'down')}
                    disabled={index === (customFields?.length || 0) - 1 || loading}
                    className="p-1.5 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    title="تحريك لأسفل"
                  >
                    <GripVertical className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Field Preview */}
              <div className="mb-4">
                <div className="bg-gray-700/30 rounded-lg p-3">
                  <label className="block text-sm font-medium mb-2 text-gray-300">
                    {field.label || 'تسمية الحقل'}
                    {field.required && <span className="text-red-400 mr-1">*</span>}
                  </label>

                  {(field.field_type === 'text' && (!field.options || field.options.length === 0)) ? (
                    <input
                      type="text"
                      placeholder={field.placeholder || 'النص التوضيحي...'}
                      className="w-full px-3 py-2 rounded border border-gray-600 bg-gray-700/50 text-white placeholder-gray-400 text-sm cursor-not-allowed"
                      disabled
                    />
                  ) : (
                    <select
                      className="w-full px-3 py-2 rounded border border-gray-600 bg-gray-700/50 text-white text-sm cursor-not-allowed"
                      disabled
                    >
                      <option>{field.placeholder || 'اختر من القائمة...'}</option>
                      {field.options && field.options.slice(0, 3).map((option, optIndex) => (
                        <option key={optIndex}>
                          {typeof option === 'string' ? option : option.label || option.value}
                        </option>
                      ))}
                      {field.options && field.options.length > 3 && (
                        <option>... و {field.options.length - 3} خيارات أخرى</option>
                      )}
                    </select>
                  )}

                  {field.description && (
                    <p className="text-gray-400 text-xs mt-1">{field.description}</p>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  onClick={() => openEditModal(index)}
                  disabled={loading}
                  className="flex items-center gap-2 px-3 py-2 text-purple-400 hover:text-purple-300 hover:bg-purple-500/10 rounded-lg transition-colors text-sm"
                >
                  <Edit3 className="w-4 h-4" />
                  تعديل
                </button>

                <button
                  type="button"
                  onClick={() => removeCustomField(index)}
                  disabled={loading}
                  className="flex items-center gap-2 px-3 py-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors text-sm"
                >
                  <Trash2 className="w-4 h-4" />
                  حذف
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Custom Field Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] flex items-center justify-center p-2 sm:p-4">
          <div className="bg-gray-900 rounded-2xl border border-gray-700 w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden shadow-2xl flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center">
                  <Type className="w-5 h-5 text-purple-400" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-white">
                    {editingField ? 'تعديل الحقل المخصص' : 'إضافة حقل مخصص'}
                  </h2>
                  <p className="text-gray-400 text-sm">
                    قم بإنشاء حقل مخصص لجمع معلومات إضافية من العملاء
                  </p>
                </div>
              </div>
              <button
                onClick={closeModal}
                disabled={loading}
                className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 p-4 sm:p-6 overflow-y-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Form Fields */}
                <div className="space-y-6">
                  {/* Label */}
                  <div>
                    <label className="block text-sm font-medium mb-2 text-white">
                      تسمية الحقل <span className="text-red-400">*</span>
                    </label>
                    <input
                      type="text"
                      value={modalFormData.label}
                      onChange={(e) => updateModalFormData({ label: e.target.value })}
                      className={`w-full px-4 py-3 rounded-xl border ${
                        modalErrors.label
                          ? 'border-red-500 focus:border-red-500'
                          : 'border-gray-600 focus:border-purple-500'
                      } bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
                      placeholder="أدخل تسمية الحقل..."
                      disabled={loading}
                    />
                    {modalErrors.label && (
                      <p className="text-red-400 text-sm mt-1">{modalErrors.label}</p>
                    )}
                  </div>

                  {/* Field Type */}
                  <div>
                    <label className="block text-sm font-medium mb-2 text-white">
                      نوع الحقل <span className="text-red-400">*</span>
                    </label>
                    <select
                      value={modalFormData.field_type}
                      onChange={(e) => updateModalFormData({
                        field_type: e.target.value as 'text' | 'dropdown',
                        options: e.target.value === 'text' ? [] : (modalFormData.options || [])
                      })}
                      className="w-full px-4 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
                      disabled={loading}
                    >
                      <option value="text">نص</option>
                      <option value="dropdown">قائمة منسدلة</option>
                    </select>
                  </div>

                  {/* Placeholder */}
                  <div>
                    <label className="block text-sm font-medium mb-2 text-white">
                      النص التوضيحي
                    </label>
                    <input
                      type="text"
                      value={modalFormData.placeholder}
                      onChange={(e) => updateModalFormData({ placeholder: e.target.value })}
                      className="w-full px-4 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
                      placeholder="أدخل النص التوضيحي..."
                      disabled={loading}
                    />
                  </div>

                  {/* Description */}
                  <div>
                    <label className="block text-sm font-medium mb-2 text-white">
                      وصف الحقل
                    </label>
                    <textarea
                      value={modalFormData.description}
                      onChange={(e) => updateModalFormData({ description: e.target.value })}
                      rows={3}
                      className="w-full px-4 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all resize-none"
                      placeholder="أدخل وصف الحقل..."
                      disabled={loading}
                    />
                  </div>

                  {/* Required */}
                  <div className="bg-gray-800/30 rounded-xl p-4">
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={modalFormData.required}
                        onChange={(e) => updateModalFormData({ required: e.target.checked })}
                        disabled={loading}
                        className="w-5 h-5 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500 focus:ring-2"
                      />
                      <div>
                        <span className="text-sm font-medium text-white">حقل مطلوب</span>
                        <p className="text-gray-400 text-xs mt-1">
                          سيكون هذا الحقل مطلوباً عند الطلب
                        </p>
                      </div>
                    </label>
                  </div>

                  {/* Options for dropdown */}
                  {modalFormData.field_type === 'dropdown' && (
                    <div>
                      <label className="block text-sm font-medium mb-3 text-white">
                        خيارات القائمة المنسدلة <span className="text-red-400">*</span>
                      </label>

                      {/* Add Option */}
                      <div className="flex gap-2 mb-4">
                        <input
                          type="text"
                          value={modalNewOption}
                          onChange={(e) => setModalNewOption(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault()
                              addModalOption()
                            }
                          }}
                          className="flex-1 px-4 py-3 rounded-xl border border-gray-600 focus:border-purple-500 bg-gray-800/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
                          placeholder="أدخل خيار جديد..."
                          disabled={loading}
                        />
                        <button
                          type="button"
                          onClick={addModalOption}
                          disabled={!modalNewOption.trim() || loading}
                          className="px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-xl transition-colors flex items-center gap-2"
                        >
                          <Plus className="w-4 h-4" />
                          إضافة
                        </button>
                      </div>

                      {/* Options List */}
                      {(modalFormData.options && modalFormData.options.length > 0) ? (
                        <div className="space-y-2 max-h-40 overflow-y-auto bg-gray-800/30 rounded-xl p-4">
                          {modalFormData.options.map((option, index) => (
                            <div
                              key={index}
                              className="flex items-center justify-between bg-gray-700/50 rounded-lg px-3 py-2"
                            >
                              <span className="text-sm text-white">
                                {typeof option === 'string' ? option : String(option.label || option.value || option)}
                              </span>
                              <button
                                type="button"
                                onClick={() => removeModalOption(index)}
                                disabled={loading}
                                className="p-1 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded transition-colors"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 border-2 border-dashed border-gray-600 rounded-xl bg-gray-800/20">
                          <ChevronDown className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-gray-400 text-sm">
                            لا توجد خيارات. أضف خيار واحد على الأقل.
                          </p>
                        </div>
                      )}

                      {modalErrors.options && (
                        <p className="text-red-400 text-sm mt-2">{modalErrors.options}</p>
                      )}
                    </div>
                  )}
                </div>

                {/* Preview */}
                <div className="space-y-6">
                  <div className="bg-gray-800/30 rounded-xl p-6">
                    <div className="flex items-center gap-2 mb-4">
                      <Eye className="w-5 h-5 text-purple-400" />
                      <h3 className="text-lg font-medium text-white">معاينة الحقل</h3>
                    </div>

                    <div className="bg-gray-700/30 rounded-xl p-4">
                      <label className="block text-sm font-medium mb-2 text-white">
                        {modalFormData.label || 'تسمية الحقل'}
                        {modalFormData.required && <span className="text-red-400 mr-1">*</span>}
                      </label>

                      {modalFormData.field_type === 'text' ? (
                        <input
                          type="text"
                          placeholder={modalFormData.placeholder || 'النص التوضيحي...'}
                          className="w-full px-4 py-3 rounded-lg border border-gray-600 bg-gray-700/50 text-white placeholder-gray-400 cursor-not-allowed"
                          disabled
                        />
                      ) : (
                        <select
                          className="w-full px-4 py-3 rounded-lg border border-gray-600 bg-gray-700/50 text-white cursor-not-allowed"
                          disabled
                        >
                          <option>{modalFormData.placeholder || 'اختر من القائمة...'}</option>
                          {(modalFormData.options || []).map((option, index) => (
                            <option key={index}>
                              {typeof option === 'string' ? option : String(option.label || option.value || option)}
                            </option>
                          ))}
                        </select>
                      )}

                      {modalFormData.description && (
                        <p className="text-gray-400 text-xs mt-2">{modalFormData.description}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end gap-3 p-4 sm:p-6 border-t border-gray-700 bg-gray-900/50 shrink-0">
              <button
                onClick={closeModal}
                disabled={loading}
                className="w-full sm:w-auto px-6 py-3 text-gray-400 hover:text-white hover:bg-gray-800 rounded-xl transition-colors order-2 sm:order-1"
              >
                إلغاء
              </button>
              <button
                onClick={saveModalField}
                disabled={loading}
                className="w-full sm:w-auto px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-xl transition-colors flex items-center justify-center gap-2 order-1 sm:order-2"
              >
                {loading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                    جاري الحفظ...
                  </>
                ) : (
                  <>
                    <Type className="w-4 h-4" />
                    {editingField ? 'تحديث الحقل' : 'إضافة الحقل'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
