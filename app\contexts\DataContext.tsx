"use client"

import Re<PERSON>, { create<PERSON>ontext, use<PERSON>ontext, useState, ReactNode } from "react"
import { cache, CACHE_KEYS } from "../lib/cache"

// Super simple data context - just cache wrapper
interface DataContextType {
  // Simple data fetching with cache
  fetchData: (key: string, url: string) => Promise<any>
  clearCache: () => void
}
const DataContext = createContext<DataContextType | undefined>(undefined)

export function useData() {
  const context = useContext(DataContext)
  if (context === undefined) {
    throw new Error("useData must be used within a DataProvider")
  }
  return context
}

export function DataProvider({ children }: { children: ReactNode }) {
  // Simple fetch with cache
  const fetchData = async (key: string, url: string) => {
    // Check cache first
    const cached = cache.get(key)
    if (cached) {
      return cached
    }

    // Fetch from API
    const response = await fetch(url)
    const data = await response.json()

    // Cache the result
    cache.set(key, data)
    return data
  }

  const clearCache = () => {
    cache.clear()
  }

  const value: DataContextType = {
    fetchData,
    clearCache
  }

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  )
}


