import { NextRequest, NextResponse } from 'next/server'

// Simple user interface
export interface AuthUser {
  id: string
  email: string
  role: 'admin' | 'distributor' | 'user' | 'worker'
  tenant_id: string
  name: string
}

// Get user from middleware headers (set by middleware)
export function getUserFromHeaders(request: NextRequest): AuthUser | null {
  const userId = request.headers.get('x-user-id')
  const userEmail = request.headers.get('x-user-email')
  const userRole = request.headers.get('x-user-role')
  const userTenant = request.headers.get('x-user-tenant')
  const userName = request.headers.get('x-user-name')

  if (!userId || !userRole || !userTenant) {
    return null
  }

  return {
    id: userId,
    email: userEmail || '',
    role: userRole as AuthUser['role'],
    tenant_id: userTenant,
    name: userName || ''
  }
}

// Check if user is admin
export function requireAdmin(user: AuthUser | null): user is AuthUser {
  return user?.role === 'admin'
}

// Check if user is worker or admin
export function requireWorkerOrAdmin(user: AuthUser | null): user is AuthUser {
  return user?.role === 'admin' || user?.role === 'worker'
}

// Simple response helpers
export function unauthorizedResponse(message: string = 'Unauthorized'): NextResponse {
  return NextResponse.json({ error: message }, { status: 401 })
}

export function forbiddenResponse(message: string = 'Forbidden'): NextResponse {
  return NextResponse.json({ error: message }, { status: 403 })
}

// Get tenant ID from headers
export function getTenantId(request: NextRequest): string {
  return request.headers.get('x-tenant-id') || process.env.DEFAULT_TENANT_ID || 'default-tenant'
}
