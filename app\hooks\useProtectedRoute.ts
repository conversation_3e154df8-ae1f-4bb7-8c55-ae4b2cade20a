"use client"

import { useAuth } from '../contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

interface UseProtectedRouteOptions {
  redirectTo?: string
  requiredRole?: 'admin' | 'user' | 'worker'
  showToast?: boolean
}

export function useProtectedRoute(options: UseProtectedRouteOptions = {}) {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !user && options.redirectTo) {
      router.push(options.redirectTo)
    }
  }, [isLoading, user, options.redirectTo, router])

  const hasRequiredRole = options.requiredRole
    ? user?.role === options.requiredRole || user?.role === 'admin'
    : true

  return {
    isLoading,
    isAuthenticated: !!user,
    user,
    hasRequiredRole
  }
}

// Specific hooks for common use cases
export function useRequireAuth(redirectTo: string = '/') {
  return useProtectedRoute({ redirectTo })
}

export function useRequireAdmin(redirectTo: string = '/') {
  return useProtectedRoute({ redirectTo, requiredRole: 'admin' })
}

// Hook for guest-only routes (redirect authenticated users)
export function useGuestRoute(redirectTo: string = '/profile') {
  const { user, isLoading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && user) {
      router.push(redirectTo)
    }
  }, [isLoading, user, redirectTo, router])

  return {
    isLoading,
    isGuest: !user
  }
}
