/**
 * Super Simple Cache System
 * - Just localStorage with 5-minute expiry
 * - No complexity, just works
 */

interface CacheEntry {
  data: any
  timestamp: number
}

class SimpleCache {
  private prefix = 'bentakon_'
  private defaultTTL = 5 * 60 * 1000 // 5 minutes

  // Get data if not expired
  get(key: string): any | null {
    try {
      const item = localStorage.getItem(this.prefix + key)
      if (!item) return null

      const entry: CacheEntry = JSON.parse(item)

      // Check if expired
      if (Date.now() - entry.timestamp > this.defaultTTL) {
        localStorage.removeItem(this.prefix + key)
        return null
      }

      return entry.data
    } catch {
      return null
    }
  }

  // Save data with timestamp
  set(key: string, data: any): void {
    try {
      const entry: CacheEntry = {
        data,
        timestamp: Date.now()
      }
      localStorage.setItem(this.prefix + key, JSON.stringify(entry))
    } catch {
      // If storage full, clear all cache and try again
      this.clear()
      try {
        localStorage.setItem(this.prefix + key, JSON.stringify({ data, timestamp: Date.now() }))
      } catch {
        // Give up if still fails
      }
    }
  }

  // Remove item from cache
  remove(key: string): void {
    localStorage.removeItem(this.prefix + key)
  }

  // Clear all cache
  clear(): void {
    const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
    keys.forEach(key => localStorage.removeItem(key))
  }

  // Get simple stats for performance monitoring
  getStats(): { size: number; keys: number } {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix))
      let totalSize = 0
      keys.forEach(key => {
        const item = localStorage.getItem(key)
        if (item) totalSize += item.length
      })
      return { size: totalSize, keys: keys.length }
    } catch (error) {
      return { size: 0, keys: 0 }
    }
  }
}

// Create singleton instance
export const cache = new SimpleCache()

// Simple hook for caching
export function useCache() {
  return cache
}

// Simple cache keys
export const CACHE_KEYS = {
  products: 'products',
  categories: 'categories',
  orders: 'orders',
  users: 'users',
  banners: 'banners'
}

export default cache
