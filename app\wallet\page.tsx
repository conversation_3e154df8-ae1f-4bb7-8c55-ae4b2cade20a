"use client"

import { useState, useEffect } from "react"
import { Wallet, Plus, MessageCircle, Clock, CheckCircle, XCircle, Key, Eye, EyeOff, <PERSON><PERSON>, Check } from "lucide-react"
import { convertAndFormatPrice } from "../utils/currency"
import { useAuth } from "../contexts/AuthContext"
import { UserBalances } from "../components/UserBalances"
import { Money } from "../components/Money"
import { getCurrencySymbol } from "../utils/currency"
import { toast } from "sonner"

interface RealOrder {
  id: string
  amount: number
  status: 'pending' | 'completed' | 'failed'
  custom_data: {
    quantity: number
    currency_code: string
    amount_in_currency: number
    payment_method: string
  }
  created_at: string
  updated_at: string
  products: {
    id: string
    title: string
    slug: string
    coverImage: string
  }
  packages: {
    id: string
    name: string
    price: number
    image: string
  }
}

export default function WalletPage() {
  // Use centralized data context
  // Simple placeholder since DataContext was simplified
  const currentUser = null
  const { user: authUser } = useAuth()

  // Ensure user is authenticated
  if (!currentUser || !authUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">يجب تسجيل الدخول أولاً</h1>
          <p className="text-gray-400">يرجى تسجيل الدخول للوصول إلى محفظتك</p>
        </div>
      </div>
    )
  }

  const user = currentUser

  const [showTopUpModal, setShowTopUpModal] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<RealOrder | null>(null)
  const [realOrders, setRealOrders] = useState<RealOrder[]>([])
  const [ordersLoading, setOrdersLoading] = useState(true)

  // Load real orders from API
  const loadOrders = async () => {
    if (!authUser) {
      return
    }

    try {
      setOrdersLoading(true)
      const response = await fetch('/api/orders')

      if (!response.ok) {
        throw new Error('Failed to fetch orders')
      }

      const data = await response.json()
      setRealOrders(data.orders || [])
    } catch (error) {
      toast.error('فشل في تحميل الطلبات')
    } finally {
      setOrdersLoading(false)
    }
  }

  useEffect(() => {
    console.log('Wallet page - Auth user changed:', authUser ? authUser.id : 'No user')
    if (authUser) {
      loadOrders()
    } else {
      console.log('Wallet page - No authenticated user, not loading orders')
      setOrdersLoading(false)
    }
  }, [authUser])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case "pending":
        return <Clock className="w-5 h-5 text-yellow-400" />
      case "failed":
        return <XCircle className="w-5 h-5 text-red-400" />
      default:
        return <Clock className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "text-green-400 bg-green-400/10"
      case "pending":
        return "text-yellow-400 bg-yellow-400/10"
      case "failed":
        return "text-red-400 bg-red-400/10"
      default:
        return "text-gray-400 bg-gray-400/10"
    }
  }

  const handleTopUpWhatsApp = () => {
    const message = `مرحباً! أريد شحن محفظتي. بريد حسابي: ${(user as any)?.email || 'غير محدد'}`
    const whatsappUrl = `https://wa.me/1234567890?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  // Order detail modal handler
  const handleViewOrderDetails = (order: RealOrder) => {
    setSelectedOrder(order)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          محفظتي
        </h1>
        <p className="text-gray-400 text-lg">إدارة رصيدك وعرض تاريخ الطلبات</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Wallet Balance Card */}
        <div className="lg:col-span-1">
          <UserBalances />
          <div className="mt-4">
            <button
              onClick={() => setShowTopUpModal(true)}
              className="w-full bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2 space-x-reverse shadow-lg bg-gradient-to-br from-purple-600 to-blue-600"
            >
              <Plus className="w-5 h-5" />
              <span>شحن المحفظة</span>
            </button>
          </div>

          {/* Quick Stats */}
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl p-6 border border-gray-700/50 shadow-xl">
            <h3 className="text-lg font-semibold mb-4">إحصائيات سريعة</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-400">إجمالي الطلبات</span>
                <span className="font-semibold">{realOrders.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">مكتملة</span>
                <span className="font-semibold text-green-400">
                  {realOrders.filter((o) => o.status === "completed").length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">قيد الانتظار</span>
                <span className="font-semibold text-yellow-400">
                  {realOrders.filter((o) => o.status === "pending").length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">فاشلة</span>
                <span className="font-semibold text-red-400">
                  {realOrders.filter((o) => o.status === "failed").length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">إجمالي المصروف</span>
                <span className="font-semibold">
                  {realOrders.reduce((sum, order) => sum + (order.custom_data?.amount_in_currency || order.amount), 0).toFixed(2)} USD
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions & Orders Link */}
        <div className="lg:col-span-2">
          <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
            <div className="p-6 border-b border-gray-700/50">
              <h2 className="text-2xl font-bold">الإجراءات السريعة</h2>
            </div>

            <div className="p-6 space-y-6">
              {/* Orders Link */}
              <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-lg p-6 border border-purple-500/30">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">طلباتي</h3>
                    <p className="text-gray-300">عرض وإدارة جميع طلباتك والأكواد الرقمية</p>
                    <div className="flex items-center space-x-4 space-x-reverse mt-3 text-sm text-gray-400">
                      <span>إجمالي الطلبات: {realOrders.length}</span>
                      <span>مكتملة: {realOrders.filter((o) => o.status === "completed").length}</span>
                    </div>
                  </div>
                  <button
                    onClick={() => window.location.href = '/orders'}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 flex items-center space-x-2 space-x-reverse"
                  >
                    <Eye className="w-5 h-5" />
                    <span>عرض الطلبات</span>
                  </button>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-700/30 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-400">{realOrders.filter((o) => o.status === "completed").length}</div>
                  <div className="text-gray-400 text-sm">طلبات مكتملة</div>
                </div>
                <div className="bg-gray-700/30 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-400">{realOrders.filter((o) => o.status === "pending").length}</div>
                  <div className="text-gray-400 text-sm">قيد الانتظار</div>
                </div>
                <div className="bg-gray-700/30 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-purple-400">{realOrders.length}</div>
                  <div className="text-gray-400 text-sm">إجمالي الطلبات</div>
                </div>
              </div>

              {/* Recent Orders Preview */}
              {realOrders.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-white">آخر الطلبات</h3>
                    <button
                      onClick={() => window.location.href = '/orders'}
                      className="text-blue-400 hover:text-blue-300 text-sm font-medium"
                    >
                      عرض الكل
                    </button>
                  </div>
                  <div className="space-y-3">
                    {realOrders.slice(0, 3).map((order) => (
                      <div key={order.id} className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/50">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-semibold text-white">{order.products?.title || "منتج غير معروف"}</h4>
                            <p className="text-gray-400 text-sm">{new Date(order.created_at).toLocaleDateString("ar")}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-white">
                              {order.custom_data?.amount_in_currency?.toFixed(2) || order.amount.toFixed(2)} {getCurrencySymbol(order.custom_data?.currency_code || 'USD')}
                            </div>
                            <div className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded text-xs ${getStatusColor(order.status)}`}>
                              {getStatusIcon(order.status)}
                              <span>
                                {order.status === "completed" ? "مكتمل" :
                                 order.status === "pending" ? "قيد الانتظار" : "فاشل"}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Top Up Modal */}
      {showTopUpModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl p-6 max-w-md w-full border border-gray-700/50 shadow-2xl">
            <h3 className="text-2xl font-bold mb-4">شحن المحفظة</h3>
            <p className="text-gray-400 mb-6">
              تواصل مع فريق الدعم عبر الواتساب لإضافة أموال إلى محفظتك. سنقوم بمعالجة طلبك بسرعة وأمان.
            </p>

            <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 mb-6 border border-gray-600/50">
              <h4 className="font-semibold mb-2">معلومات حسابك:</h4>
              <p className="text-sm text-gray-300">البريد الإلكتروني: {(user as any)?.email || 'غير محدد'}</p>
              <p className="text-sm text-gray-300">الرصيد الحالي: ${(user as any)?.walletBalance?.toFixed(2) || '0.00'}</p>
            </div>

            <div className="flex space-x-3 space-x-reverse">
              <button
                onClick={handleTopUpWhatsApp}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2 space-x-reverse shadow-lg"
              >
                <MessageCircle className="w-5 h-5" />
                <span>تواصل عبر الواتساب</span>
              </button>
              <button onClick={() => setShowTopUpModal(false)} className="btn-secondary">
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl p-6 max-w-lg w-full border border-gray-700/50 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold">تفاصيل الطلب</h3>
              <button
                onClick={() => setSelectedOrder(null)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>

            {/* Order Details */}
            <div className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-4 mb-6 border border-gray-600/50">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">اسم المنتج:</span>
                  <p className="font-semibold">{selectedOrder.products?.title || 'غير محدد'}</p>
                </div>
                <div>
                  <span className="text-gray-400">المبلغ:</span>
                  <p className="font-semibold">
                    {selectedOrder.custom_data?.amount_in_currency?.toFixed(2) || selectedOrder.amount.toFixed(2)} {getCurrencySymbol(selectedOrder.custom_data?.currency_code || 'USD')}
                  </p>
                </div>
                <div>
                  <span className="text-gray-400">الحزمة:</span>
                  <p className="font-semibold">{selectedOrder.packages?.name || 'غير محدد'}</p>
                </div>
                <div>
                  <span className="text-gray-400">الكمية:</span>
                  <p className="font-semibold">{selectedOrder.custom_data?.quantity || 1}</p>
                </div>
                <div>
                  <span className="text-gray-400">طريقة الدفع:</span>
                  <p className="font-semibold">{selectedOrder.custom_data?.payment_method === 'wallet' ? 'محفظة' : 'دفع خارجي'}</p>
                </div>
                <div>
                  <span className="text-gray-400">تاريخ الطلب:</span>
                  <p className="font-semibold">{new Date(selectedOrder.created_at).toLocaleString('ar-SA')}</p>
                </div>
                <div>
                  <span className="text-gray-400">الحالة:</span>
                  <div
                    className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded text-xs ${getStatusColor(selectedOrder.status)}`}
                  >
                    {getStatusIcon(selectedOrder.status)}
                    <span>
                      {selectedOrder.status === "completed" ? "مكتمل" :
                       selectedOrder.status === "pending" ? "قيد المعالجة" : "فشل"}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Status */}
            {selectedOrder.status === "completed" && (
              <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-4">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-green-400 font-semibold">تم إكمال الطلب بنجاح</span>
                </div>
                <p className="text-sm text-gray-400 mt-2">
                  سيتم التواصل معك قريباً لتسليم المحتوى الرقمي
                </p>
              </div>
            )}

            {/* Close Button */}
            <div className="flex justify-end">
              <button
                onClick={() => setSelectedOrder(null)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
