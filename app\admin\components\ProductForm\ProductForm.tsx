"use client"

import { useState, useEffect } from 'react'
import { Save, X, Package, DollarSign, Settings, FileText, ChevronLeft, ChevronRight } from 'lucide-react'
import BasicInfoTab from './BasicInfoTab'
import PricingTab from './PricingTab'
import PackagesTab from './PackagesTab'
import CustomFieldsTab from './CustomFieldsTab'
import type { Product, Category } from '../../../types'

interface ProductFormData {
  // Basic Info
  title: string
  slug: string
  description: string
  category_id: string
  cover_image: string
  tags: string[]
  featured: boolean

  // Pricing
  original_price: number
  user_price: number
  discount_price?: number
  distributor_price?: number

  // Inventory (for products without packages)
  manual_quantity?: number
  track_inventory?: boolean
  unlimited_stock?: boolean
  has_digital_codes?: boolean
  digital_codes?: string

  // Packages
  packages: any[]

  // Custom Fields
  customFields: any[]
}

interface ProductFormProps {
  product?: Product
  categories: Category[]
  onSubmit: (data: ProductFormData) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export default function ProductForm({
  product,
  categories,
  onSubmit,
  onCancel,
  loading = false
}: ProductFormProps) {
  const [activeTab, setActiveTab] = useState(0)
  const [formData, setFormData] = useState<ProductFormData>({
    title: '',
    slug: '',
    description: '',
    category_id: '',
    cover_image: '',
    tags: [],
    featured: false,
    original_price: 0,
    user_price: 0,
    manual_quantity: 0,
    track_inventory: false,
    unlimited_stock: true,
    has_digital_codes: false,
    digital_codes: '',
    packages: [],
    customFields: []
  })
  const [errors, setErrors] = useState<Record<string, any>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form data from product
  useEffect(() => {
    if (product) {
      setFormData({
        title: product.title || '',
        slug: product.slug || '',
        description: product.description || '',
        category_id: product.category_id || '',
        cover_image: product.cover_image || '',
        tags: product.tags || [],
        featured: product.featured || false,
        original_price: product.original_price || 0,
        user_price: product.user_price || 0,
        discount_price: product.discount_price,
        distributor_price: product.distributor_price,
        manual_quantity: (product as any).manual_quantity || 0,
        track_inventory: (product as any).track_inventory || false,
        unlimited_stock: (product as any).unlimited_stock || true,
        has_digital_codes: (product as any).has_digital_codes || false,
        packages: product.packages || [],
        customFields: (product as any).custom_fields || []
      })
    }
  }, [product])

  const tabs = [
    {
      id: 0,
      name: 'المعلومات الأساسية',
      icon: FileText,
      component: BasicInfoTab
    },
    {
      id: 1,
      name: 'التسعير',
      icon: DollarSign,
      component: PricingTab,
      hidden: formData.packages.length > 0 // Hide if packages exist
    },
    {
      id: 2,
      name: 'الحزم',
      icon: Package,
      component: PackagesTab
    },
    {
      id: 3,
      name: 'الحقول المخصصة',
      icon: Settings,
      component: CustomFieldsTab
    }
  ].filter(tab => !tab.hidden)

  const updateFormData = (updates: Partial<ProductFormData>) => {
    const prevData = formData
    setFormData(prev => ({ ...prev, ...updates }))

    // Handle tab switching when packages are added/removed
    if (updates.packages !== undefined) {
      const hadPackages = prevData.packages.length > 0
      const hasPackages = updates.packages.length > 0

      // If packages were added for the first time, switch to packages tab
      if (!hadPackages && hasPackages) {
        // Switch to packages tab (index 2 in the original tabs, but need to find actual index)
        setTimeout(() => {
          const currentTabs = [
            { id: 0, name: 'المعلومات الأساسية', icon: FileText },
            { id: 1, name: 'التسعير', icon: DollarSign, hidden: hasPackages },
            { id: 2, name: 'الحزم', icon: Package },
            { id: 3, name: 'الحقول المخصصة', icon: Settings }
          ].filter(tab => !tab.hidden)

          const packagesTabIndex = currentTabs.findIndex(tab => tab.id === 2)
          if (packagesTabIndex !== -1) {
            setActiveTab(packagesTabIndex)
          }
        }, 0)
      }

      // If all packages were removed, switch to pricing tab
      if (hadPackages && !hasPackages) {
        setTimeout(() => {
          const currentTabs = [
            { id: 0, name: 'المعلومات الأساسية', icon: FileText },
            { id: 1, name: 'التسعير', icon: DollarSign },
            { id: 2, name: 'الحزم', icon: Package },
            { id: 3, name: 'الحقول المخصصة', icon: Settings }
          ]

          const pricingTabIndex = currentTabs.findIndex(tab => tab.id === 1)
          if (pricingTabIndex !== -1) {
            setActiveTab(pricingTabIndex)
          }
        }, 0)
      }
    }

    // Clear related errors
    const newErrors = { ...errors }
    Object.keys(updates).forEach(key => {
      delete newErrors[key]
    })
    setErrors(newErrors)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})

    try {
      await onSubmit(formData)
    } catch (error: any) {
      if (error.details) {
        setErrors(error.details)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const getCurrentTabComponent = () => {
    const currentTab = tabs[activeTab]
    if (!currentTab) return null

    const Component = currentTab.component

    switch (currentTab.id) {
      case 0: // Basic Info
        return (
          <BasicInfoTab
            formData={{
              title: formData.title,
              slug: formData.slug,
              description: formData.description,
              category_id: formData.category_id,
              cover_image: formData.cover_image,
              tags: formData.tags,
              featured: formData.featured
            }}
            onChange={updateFormData}
            errors={errors}
            categories={categories}
            loading={loading || isSubmitting}
          />
        )
      
      case 1: // Pricing
        return (
          <PricingTab
            formData={{
              original_price: formData.original_price,
              user_price: formData.user_price,
              discount_price: formData.discount_price,
              distributor_price: formData.distributor_price,
              manual_quantity: formData.manual_quantity,
              track_inventory: formData.track_inventory,
              unlimited_stock: formData.unlimited_stock,
              has_digital_codes: formData.has_digital_codes
            }}
            onChange={updateFormData}
            errors={errors}
            loading={loading || isSubmitting}
            hasPackages={formData.packages.length > 0}
          />
        )
      
      case 2: // Packages
        return (
          <PackagesTab
            packages={formData.packages}
            onChange={(packages: any[]) => updateFormData({ packages })}
            errors={errors}
            loading={loading || isSubmitting}
          />
        )
      
      case 3: // Custom Fields
        return (
          <CustomFieldsTab
            customFields={formData.customFields}
            onChange={(customFields: any[]) => updateFormData({ customFields })}
            errors={errors}
            loading={loading || isSubmitting}
          />
        )
      
      default:
        return null
    }
  }

  return (
    <div className="h-full flex flex-col">
      <form onSubmit={handleSubmit} className="h-full flex flex-col">
        {/* Modern Header */}
        <div className="flex-shrink-0 bg-gray-900/50 backdrop-blur-sm border-b border-gray-700/50 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                type="button"
                onClick={onCancel}
                disabled={loading || isSubmitting}
                className="lg:hidden flex items-center justify-center w-10 h-10 rounded-full bg-gray-800 hover:bg-gray-700 text-gray-400 hover:text-white transition-colors disabled:opacity-50"
              >
                <X className="w-5 h-5" />
              </button>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-white">
                  {product ? 'تعديل المنتج' : 'إضافة منتج جديد'}
                </h1>
                <p className="text-sm text-gray-400 mt-1">
                  {tabs[activeTab]?.name} ({activeTab + 1} من {tabs.length})
                </p>
              </div>
            </div>

            {/* Desktop Actions */}
            <div className="hidden lg:flex items-center gap-3">
              <button
                type="button"
                onClick={onCancel}
                disabled={loading || isSubmitting}
                className="flex items-center gap-2 px-4 py-2 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <X className="w-4 h-4" />
                إلغاء
              </button>
              <button
                type="submit"
                disabled={loading || isSubmitting}
                className="flex items-center gap-2 px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                <Save className="w-4 h-4" />
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ'}
              </button>
            </div>
          </div>
        </div>

        {/* Modern Mobile Tab Navigation */}
        <div className="flex-shrink-0 lg:hidden bg-gray-900/30 border-b border-gray-700/50">
          <div className="flex items-center justify-between p-4">
            <button
              type="button"
              onClick={() => setActiveTab(Math.max(0, activeTab - 1))}
              disabled={activeTab === 0 || loading || isSubmitting}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-800 hover:bg-gray-700 disabled:bg-gray-800/50 disabled:cursor-not-allowed text-gray-400 hover:text-white transition-colors"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>

            <div className="flex-1 mx-4">
              <div className="flex items-center justify-center gap-2">
                {tabs.map((tab, index) => {
                  const Icon = tab.icon
                  const isActive = activeTab === index

                  return (
                    <button
                      key={tab.id}
                      type="button"
                      onClick={() => setActiveTab(index)}
                      disabled={loading || isSubmitting}
                      className={`flex flex-col items-center gap-1 p-2 rounded-lg transition-colors disabled:opacity-50 min-w-0 ${
                        isActive
                          ? 'bg-purple-600/20 text-purple-400'
                          : 'text-gray-500 hover:text-gray-300 hover:bg-gray-800/50'
                      }`}
                    >
                      <Icon className="w-4 h-4 flex-shrink-0" />
                      <span className="text-xs truncate max-w-16">{tab.name}</span>
                    </button>
                  )
                })}
              </div>
            </div>

            <button
              type="button"
              onClick={() => setActiveTab(Math.min(tabs.length - 1, activeTab + 1))}
              disabled={activeTab === tabs.length - 1 || loading || isSubmitting}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-800 hover:bg-gray-700 disabled:bg-gray-800/50 disabled:cursor-not-allowed text-gray-400 hover:text-white transition-colors"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Desktop Tab Navigation */}
        <div className="hidden lg:block flex-shrink-0 bg-gray-900/30 border-b border-gray-700/50">
          <nav className="flex px-6">
            {tabs.map((tab, index) => {
              const Icon = tab.icon
              const isActive = activeTab === index

              return (
                <button
                  key={tab.id}
                  type="button"
                  onClick={() => setActiveTab(index)}
                  disabled={loading || isSubmitting}
                  className={`flex items-center gap-3 py-4 px-6 border-b-2 font-medium text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                    isActive
                      ? 'border-purple-500 text-purple-400 bg-purple-500/5'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Scrollable Tab Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4 sm:p-6">
            <div className="max-w-4xl mx-auto">
              {getCurrentTabComponent()}
            </div>
          </div>
        </div>

        {/* Mobile Bottom Actions */}
        <div className="lg:hidden flex-shrink-0 bg-gray-900/50 backdrop-blur-sm border-t border-gray-700/50 p-4">
          <div className="flex gap-3">
            <button
              type="button"
              onClick={onCancel}
              disabled={loading || isSubmitting}
              className="flex-1 flex items-center justify-center gap-2 px-4 py-3 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <X className="w-4 h-4" />
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading || isSubmitting}
              className="flex-2 flex items-center justify-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              <Save className="w-4 h-4" />
              {isSubmitting ? 'جاري الحفظ...' : 'حفظ المنتج'}
            </button>
          </div>
        </div>
      </form>
    </div>
  )
}
