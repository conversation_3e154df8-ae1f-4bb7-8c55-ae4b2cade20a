import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Get tenant slug from headers (set by middleware) or default to 'main'
    const tenantSlug = request.headers.get('x-tenant-slug') || 'main'
    
    // Get tenant ID
    const { data: tenant } = await supabase
      .from('tenants')
      .select('id')
      .eq('slug', tenantSlug)
      .single()

    if (!tenant) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 })
    }

    // Fetch active banner slides for the tenant
    const { data: banners, error } = await supabase
      .from('banner_slides')
      .select('*')
      .eq('tenant_id', tenant.id)
      .eq('active', true)
      .order('order_index')

    if (error) {
      console.error('Error fetching banners:', error)
      return NextResponse.json({ error: 'Failed to fetch banners' }, { status: 500 })
    }

    // Transform data to match expected format
    const transformedBanners = banners.map(banner => ({
      id: banner.id,
      title: banner.title,
      subtitle: banner.subtitle,
      image: banner.image,
      linkType: banner.link_type,
      linkValue: banner.link_value,
      active: banner.active,
      order: banner.order_index
    }))

    return NextResponse.json(transformedBanners)
  } catch (error) {
    console.error('Error in banners API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
