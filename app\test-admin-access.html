<!DOCTYPE html>
<html>
<head>
    <title>Admin Access Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #10b981; }
        .error { background: #ef4444; }
        .info { background: #3b82f6; }
    </style>
</head>
<body>
    <h1>🔍 Admin Access <NAME_EMAIL></h1>
    
    <div class="test-result success">
        ✅ <strong>Database Check:</strong> User exists with admin role
        <br>• Email: <EMAIL>
        <br>• Role: admin
        <br>• Tenant ID: caf1844f-4cc2-4c17-a775-1c837ae01051
    </div>

    <div class="test-result success">
        ✅ <strong>Auth Session:</strong> Active session found
        <br>• Last sign in: 2025-07-25 09:33:11
        <br>• User ID: 0c338af3-9288-44a8-86bc-86dec31d2830
    </div>

    <div class="test-result info">
        🔧 <strong>Fixes Applied:</strong>
        <br>• Fixed admin page to use AuthContext instead of DataContext
        <br>• Added loading state handling
        <br>• Synced currentUser between contexts
        <br>• Added debug component for troubleshooting
    </div>

    <div class="test-result info">
        📋 <strong>Next Steps:</strong>
        <br>1. Clear your browser cache (Ctrl+Shift+R)
        <br>2. Make sure you're logged <NAME_EMAIL>
        <br>3. Visit <a href="/admin" style="color: #60a5fa;">/admin</a>
        <br>4. Check the debug info in bottom-right corner
        <br>5. If still blocked, check browser console for errors
    </div>

    <div class="test-result info">
        🚀 <strong>Admin URL:</strong>
        <br><a href="/admin" style="color: #60a5fa; font-size: 18px;">http://localhost:3000/admin</a>
    </div>

    <script>
        // Auto-redirect to admin page after 3 seconds
        setTimeout(() => {
            if (confirm('Redirect to admin page now?')) {
                window.location.href = '/admin';
            }
        }, 3000);
    </script>
</body>
</html>
