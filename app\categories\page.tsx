"use client"

import { useState, useEffect, useMemo } from "react"
import { Tag, Search } from "lucide-react"
import CategoryCard from "../components/CategoryCard"
import { LoadingSpinner } from "../components/LoadingStates"
import { useTenant } from "../contexts/TenantContext"
import { getCategories } from "../lib/categories"
import type { Category } from "../types"

export default function CategoriesPage() {
  const { tenant } = useTenant()
  // Simple state management since DataContext was simplified
  const [products, setProducts] = useState<any[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")

  // Load categories when component mounts
  useEffect(() => {
    if (tenant) {
      loadCategories()
    }
  }, [tenant])

  const loadCategories = async () => {
    if (!tenant) return

    setIsLoading(true)
    try {
      const result = await getCategories(tenant.id)
      if (result.success && result.data) {
        setCategories(result.data)
      } else {
        console.error('Failed to load categories:', result.error)
      }
    } catch (error) {
      console.error('Error loading categories:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Calculate product count for each category
  const categoriesWithProductCount = useMemo(() => {
    return categories.map(category => {
      const productCount = products.filter(product => 
        product.category_id === category.id || product.category === category.name
      ).length
      return { ...category, product_count: productCount }
    })
  }, [categories, products])

  // Filter categories based on search query
  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) return categoriesWithProductCount
    
    const query = searchQuery.toLowerCase()
    return categoriesWithProductCount.filter(category =>
      (category.name?.toLowerCase().includes(query)) ||
      category.slug.toLowerCase().includes(query) ||
      (category.description?.toLowerCase().includes(query))
    )
  }, [categoriesWithProductCount, searchQuery])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <LoadingSpinner size="lg" className="text-purple-500 mx-auto mb-4" />
              <p className="text-gray-400">جاري تحميل الفئات...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="container mx-auto px-4 py-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6 md:mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
              فئات المنتجات
            </h1>
            <p className="text-gray-400">
              استكشف منتجاتنا حسب الفئة
            </p>
          </div>
          
          {/* Search */}
          <div className="relative w-full sm:w-80">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="البحث في الفئات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-lg px-10 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
            />
          </div>
        </div>

        {/* Categories Grid */}
        {filteredCategories.length === 0 ? (
          <div className="text-center py-16">
            <Tag className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-300 mb-2">
              {searchQuery ? 'لا توجد فئات مطابقة' : 'لا توجد فئات متاحة'}
            </h3>
            <p className="text-gray-400 mb-6">
              {searchQuery 
                ? 'جرب البحث بكلمات مختلفة' 
                : 'لم يتم إنشاء أي فئات بعد'
              }
            </p>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery("")}
                className="btn-secondary"
              >
                مسح البحث
              </button>
            )}
          </div>
        ) : (
          <>
            {/* Results count */}
            <div className="flex items-center justify-between mb-6">
              <p className="text-gray-400">
                {searchQuery 
                  ? `${filteredCategories.length} فئة مطابقة للبحث`
                  : `${filteredCategories.length} فئة متاحة`
                }
              </p>
            </div>

            {/* Categories Grid - Responsive */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 md:gap-4">
              {filteredCategories.map((category) => (
                <CategoryCard
                  key={category.id}
                  category={category}
                  productCount={category.product_count}
                />
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  )
}
