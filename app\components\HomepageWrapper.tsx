"use client"

import { useEffect, useState } from "react"
import AdminSetup from "./AdminSetup"
import Tenant<PERSON>witcher from "./TenantSwitcher"
import { useAuth } from "../contexts/AuthContext"

interface HomepageWrapperProps {
  children: React.ReactNode
  initialProducts: any[]
  initialCategories: any[]
  initialHomepageSections: any[]
  tenant: any
}

/**
 * Client wrapper for homepage - handles hydration and client-side features
 * while keeping the main page as SSR for better SEO
 */
export default function HomepageWrapper({ 
  children, 
  initialProducts, 
  initialCategories, 
  initialHomepageSections,
  tenant 
}: HomepageWrapperProps) {
  // Simple placeholders since DataContext was simplified
  const [products, setProducts] = useState<any[]>([])
  const [hydrated, setHydrated] = useState(false)
  const { user: currentUser } = useAuth()

  // Hydrate client-side data with SSR data (run only once)
  useEffect(() => {
    if (!hydrated) {
      if (initialProducts && initialProducts.length > 0) {
        // Map products to the expected format
        const mappedProducts = initialProducts.map(product => ({
          id: product.id,
          title: product.title,
          slug: product.slug,
          description: product.description,
          cover_image: product.cover_image,
          category_id: product.category_id,
          featured: product.featured,
          original_price: product.original_price,
          user_price: product.user_price,
          discount_price: product.discount_price,
          distributor_price: product.distributor_price,
          created_at: product.created_at,
          updated_at: product.updated_at,
          tenant_id: product.tenant_id,
          categories: product.categories,
          packages: product.packages || [],
          custom_fields: product.custom_fields || [],
          dropdowns: product.dropdowns || []
        }))

        setProducts(mappedProducts)
        console.log('✅ Client: Hydrated products from SSR:', mappedProducts.length)
      }

      if (initialCategories && initialCategories.length > 0) {
        // Categories are handled directly in SSR, no client-side state needed
        console.log('✅ Client: Categories available from SSR:', initialCategories.length)
      }

      if (initialHomepageSections && initialHomepageSections.length > 0) {
        console.log('✅ Client: Hydrated homepage sections from SSR:', initialHomepageSections.length)
      }

      setHydrated(true)
    }
  }, [initialProducts, initialCategories, initialHomepageSections, hydrated])

  return (
    <div>
      {/* Admin-only components */}
      {currentUser?.role === 'admin' && (
        <>
          <AdminSetup />
          <TenantSwitcher />
        </>
      )}
      
      {/* SSR content */}
      {children}
    </div>
  )
}
