/**
 * Multi-currency utility functions for the Bentakon store
 * Handles currency formatting, conversion, and management
 */

import { createClient } from '@supabase/supabase-js'

// Note: Using API endpoints instead of direct database access for better client-side compatibility

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Currency interface
export interface Currency {
  code: string
  name: string
  exchange_rate: number
  is_active: boolean
}

// Cache for currencies to avoid repeated database calls
let currencyCache: Map<string, Currency[]> = new Map()
let cacheExpiry: Map<string, number> = new Map()
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

/**
 * Get all active currencies for a tenant
 */
export async function getCurrencies(tenantId: string): Promise<Currency[]> {
  const now = Date.now()
  const cached = currencyCache.get(tenantId)
  const expiry = cacheExpiry.get(tenantId)

  if (cached && expiry && now < expiry) {
    return cached
  }

  try {
    // Use API endpoint instead of direct database access
    const response = await fetch('/api/currencies', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      console.warn(`Currency API returned ${response.status}, using fallback`)
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    console.log('Currency API response:', data)

    let currencies = data.currencies || []

    // Ensure we have at least USD as fallback
    if (currencies.length === 0) {
      console.log('No currencies returned from API, adding USD fallback')
      currencies = [{
        code: 'USD',
        name: 'US Dollar',
        exchange_rate: 1.0,
        is_active: true
      }]
    }

    currencyCache.set(tenantId, currencies)
    cacheExpiry.set(tenantId, now + CACHE_TTL)

    return currencies
  } catch (error) {
    console.error('Error fetching currencies from API:', error)

    // Return comprehensive fallback currencies
    const fallback = [
      {
        code: 'USD',
        name: 'US Dollar',
        exchange_rate: 1.0,
        is_active: true
      },
      {
        code: 'SDG',
        name: 'Sudanese Pound',
        exchange_rate: 600.0,
        is_active: true
      }
    ]

    console.log('Using fallback currencies:', fallback)
    currencyCache.set(tenantId, fallback)
    cacheExpiry.set(tenantId, now + CACHE_TTL)

    return fallback
  }
}

/**
 * Convert USD price to target currency
 */
export async function convertPrice(
  usdPrice: number,
  targetCurrency: string,
  tenantId: string
): Promise<number> {
  if (targetCurrency === 'USD') {
    return usdPrice
  }

  const currencies = await getCurrencies(tenantId)
  const currency = currencies.find(c => c.code === targetCurrency)

  if (!currency) {
    console.warn(`Currency ${targetCurrency} not found, defaulting to USD`)
    return usdPrice
  }

  return Number((usdPrice * currency.exchange_rate).toFixed(2))
}

/**
 * Format price with currency code
 */
export function formatPrice(amount: number, currency: Currency): string {
  const symbol = getCurrencySymbol(currency.code as any) || currency.code
  return `${amount.toFixed(2)} ${symbol}`
}

/**
 * Format price in USD (backward compatibility)
 */
export function formatUSD(amount: number): string {
  return `$${amount.toFixed(2)}`
}

/**
 * Convert and format price (backward compatibility)
 * Now converts from USD to user's preferred currency
 */
export async function convertAndFormatPrice(
  usdAmount: number,
  targetCurrency: string = 'USD',
  tenantId?: string
): Promise<string> {
  if (!tenantId || targetCurrency === 'USD') {
    return formatUSD(usdAmount)
  }

  try {
    const convertedAmount = await convertPrice(usdAmount, targetCurrency, tenantId)
    const currencies = await getCurrencies(tenantId)
    const currency = currencies.find(c => c.code === targetCurrency)

    if (currency) {
      return formatPrice(convertedAmount, currency)
    }

    return formatUSD(usdAmount)
  } catch (error) {
    console.error('Error in convertAndFormatPrice:', error)
    return formatUSD(usdAmount)
  }
}

/**
 * Get user's balance for a specific currency
 */
export async function getUserBalance(
  userId: string,
  currencyCode: string
): Promise<number> {
  try {
    const { data: balance, error } = await supabase
      .from('user_currency_balances')
      .select('balance')
      .eq('user_id', userId)
      .eq('currency_code', currencyCode)
      .single()

    if (error || !balance) {
      return 0
    }

    return balance.balance
  } catch (error) {
    console.error('Error getting user balance:', error)
    return 0
  }
}

/**
 * Update user balance with audit logging
 */
export async function updateUserBalance(
  userId: string,
  currencyCode: string,
  amount: number,
  changeType: 'deposit' | 'purchase' | 'admin_adjustment' | 'refund',
  referenceId?: string,
  adminId?: string,
  notes?: string
): Promise<boolean> {
  try {
    // Get current balance and tenant info
    const { data: currentData } = await supabase
      .from('user_currency_balances')
      .select('balance, tenant_id')
      .eq('user_id', userId)
      .eq('currency_code', currencyCode)
      .single()

    const balanceBefore = currentData?.balance || 0
    const balanceAfter = balanceBefore + amount

    if (balanceAfter < 0) {
      throw new Error('Insufficient balance')
    }

    // Update balance
    const { error: updateError } = await supabase
      .from('user_currency_balances')
      .upsert({
        user_id: userId,
        currency_code: currencyCode,
        balance: balanceAfter,
        tenant_id: currentData?.tenant_id,
        updated_at: new Date().toISOString()
      })

    if (updateError) throw updateError

    // Log the change
    const { error: logError } = await supabase
      .from('balance_change_log')
      .insert({
        user_id: userId,
        currency_code: currencyCode,
        amount_change: amount,
        balance_before: balanceBefore,
        balance_after: balanceAfter,
        change_type: changeType,
        reference_id: referenceId,
        admin_id: adminId,
        notes: notes,
        tenant_id: currentData?.tenant_id
      })

    if (logError) throw logError

    return true
  } catch (error) {
    console.error('Error updating user balance:', error)
    return false
  }
}

// Legacy functions for backward compatibility
export function convertEurToUsd(eurAmount: number): number {
  return eurAmount * 1.08 // Keep old hardcoded rate for backward compatibility
}

export function getCurrencySymbol(currency: string = 'USD'): string {
  const symbols: Record<string, string> = {
    // Major currencies
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'CNY': '¥',
    'KRW': '₩',
    'INR': '₹',
    'RUB': '₽',

    // Middle East & Africa
    'SAR': 'ر.س',
    'AED': 'د.إ',
    'QAR': 'ر.ق',
    'KWD': 'د.ك',
    'BHD': 'د.ب',
    'OMR': 'ر.ع',
    'JOD': 'د.أ',
    'LBP': 'ل.ل',
    'EGP': 'ج.م',
    'MAD': 'د.م',
    'TND': 'د.ت',
    'DZD': 'د.ج',
    'SDG': 'ج.س',
    'IQD': 'د.ع',
    'SYP': 'ل.س',
    'YER': 'ر.ي',

    // Other common currencies
    'CAD': 'C$',
    'AUD': 'A$',
    'CHF': 'Fr',
    'SEK': 'kr',
    'NOK': 'kr',
    'DKK': 'kr',
    'PLN': 'zł',
    'CZK': 'Kč',
    'HUF': 'Ft',
    'RON': 'lei',
    'BGN': 'лв',
    'HRK': 'kn',
    'RSD': 'дин',
    'TRY': '₺',
    'ZAR': 'R',
    'NGN': '₦',
    'KES': 'KSh',
    'GHS': '₵',
    'ETB': 'Br',
    'UGX': 'USh',
    'TZS': 'TSh',
    'RWF': 'RF',
    'MWK': 'MK',
    'ZMW': 'ZK',
    'BWP': 'P',
    'SZL': 'L',
    'LSL': 'L',
    'NAD': 'N$',
    'MZN': 'MT',
    'AOA': 'Kz',
    'XAF': 'FCFA',
    'XOF': 'CFA',
    'MGA': 'Ar',
    'KMF': 'CF',
    'SCR': '₨',
    'MUR': '₨',
    'MVR': 'Rf',
    'LKR': '₨',
    'PKR': '₨',
    'BDT': '৳',
    'NPR': '₨',
    'BTN': 'Nu',
    'AFN': '؋',
    'IRR': '﷼',
    'AMD': '֏',
    'GEL': '₾',
    'AZN': '₼',
    'KZT': '₸',
    'UZS': 'soʻm',
    'KGS': 'с',
    'TJS': 'SM',
    'TMT': 'T',
    'MNT': '₮'
  }
  return symbols[currency] || currency
}
