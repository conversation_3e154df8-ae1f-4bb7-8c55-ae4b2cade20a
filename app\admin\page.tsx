"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Users, Package, BarChart3, Home, Menu, X, Tag, Coins } from "lucide-react"
import { useRequireAdmin } from "../hooks/useProtectedRoute"

// Import actual admin components
import ProductManagement from "./components/ProductManagement"
import CategoryManagement from "./components/CategoryManagement"
import UserManagement from "./components/UserManagement"
import OrderManagement from "./components/OrderManagement"
import HomepageManagement from "./components/HomepageManagement"

// Simple currencies component
const CurrenciesAdmin = () => (
  <div className="p-6 bg-gray-800 rounded-lg">
    <h2 className="text-xl font-bold mb-4">إدارة العملات</h2>
    <p className="text-gray-400">صفحة إدارة العملات قيد التطوير</p>
  </div>
)

export default function AdminDashboard() {
  const { isLoading, hasRequiredRole } = useRequireAdmin('/')
  console.log('🔍 AdminDashboard - isLoading:', isLoading, 'hasRequiredRole:', hasRequiredRole)
  const router = useRouter()
  const searchParams = useSearchParams()

  const getInitialTab = () => searchParams.get('tab') || "overview"
  const [activeTab, setActiveTab] = useState(getInitialTab())
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Data state for admin components
  const [products, setProducts] = useState([])
  const [categories, setCategories] = useState([])
  const [users, setUsers] = useState([])
  const [orders, setOrders] = useState([])
  const [dataLoading, setDataLoading] = useState(false)

  // Memoize fetchAdminData to prevent unnecessary re-renders
  const fetchAdminData = useCallback(async () => {
    setDataLoading(true)
    try {
      // Fetch data based on active tab to avoid loading everything at once
      if (activeTab === 'products' || activeTab === 'overview') {
        const response = await fetch('/api/admin/products')
        console.log('Products API response status:', response.status, response.ok)
        if (response.ok) {
          const result = await response.json()
          console.log('Products API response:', result)
          console.log('result.data type:', typeof result.data, 'length:', result.data?.length)
          // Handle different response structures with better validation
          const productsData = result.data || result || []
          console.log('productsData type:', typeof productsData, 'isArray:', Array.isArray(productsData), 'length:', productsData?.length)

          // Ensure we always set a valid array
          if (Array.isArray(productsData)) {
            console.log('Setting products array with', productsData.length, 'items')
            setProducts(productsData)
          } else {
            console.warn('Invalid products data received, setting empty array:', productsData)
            setProducts([])
          }
        } else {
          console.log('Products API failed with status:', response.status)
          const errorResult = await response.json().catch(() => ({}))
          console.log('Error response:', errorResult)
          setProducts([]) // Set empty array on error
        }
      }

      if (activeTab === 'categories' || activeTab === 'overview') {
        const response = await fetch('/api/admin/categories')
        if (response.ok) {
          const result = await response.json()
          console.log('Categories API response:', result)
          const categoriesData = result.data || result || []
          setCategories(Array.isArray(categoriesData) ? categoriesData : [])
        }
      }

      if (activeTab === 'users') {
        const response = await fetch('/api/admin/users')
        if (response.ok) {
          const result = await response.json()
          console.log('Users API response:', result)
          const usersData = result.data || result || []
          setUsers(Array.isArray(usersData) ? usersData : [])
        }
      }

      if (activeTab === 'orders') {
        const response = await fetch('/api/admin/orders')
        if (response.ok) {
          const result = await response.json()
          console.log('Orders API response:', result)
          const ordersData = result.data || result || []
          setOrders(Array.isArray(ordersData) ? ordersData : [])
        }
      }
    } catch (error) {
      console.error('Error fetching admin data:', error)
    } finally {
      setDataLoading(false)
    }
  }, [activeTab]) // Only depend on activeTab

  // Fetch data when component mounts or tab changes
  useEffect(() => {
    if (hasRequiredRole && !isLoading) {
      fetchAdminData()
    }
  }, [hasRequiredRole, isLoading, activeTab, fetchAdminData])

  // Instant tab switching
  const handleTabChange = (tab: string) => {
    setActiveTab(tab)
    router.push(`/admin?tab=${tab}`, { scroll: false })
    setIsMobileMenuOpen(false)
  }
  if (isLoading) return <div className="p-8 text-center">جاري التحميل...</div>
  if (!hasRequiredRole) return <div className="p-8 text-center text-red-400">غير مصرح بالوصول</div>

  const tabs = [
    { id: "overview", label: "نظرة عامة", icon: BarChart3 },
    { id: "products", label: "المنتجات", icon: Package },
    { id: "categories", label: "الفئات", icon: Tag },
    { id: "currencies", label: "العملات", icon: Coins },
    { id: "users", label: "المستخدمون", icon: Users },
    { id: "orders", label: "الطلبات", icon: Package },
    { id: "homepage", label: "الصفحة الرئيسية", icon: Home },
  ]

  const renderTabContent = () => {
    if (dataLoading) {
      return <div className="p-6 bg-gray-800 rounded-lg text-center">جاري تحميل البيانات...</div>
    }

    switch (activeTab) {
      case 'overview':
        return (
          <div className="p-6 bg-gray-800 rounded-lg">
            <h2 className="text-xl font-bold mb-4">نظرة عامة على المتجر</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold">المنتجات</h3>
                <p className="text-2xl font-bold text-purple-400">{products.length}</p>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold">الفئات</h3>
                <p className="text-2xl font-bold text-blue-400">{categories.length}</p>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold">المستخدمون</h3>
                <p className="text-2xl font-bold text-green-400">{users.length}</p>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-lg font-semibold">الطلبات</h3>
                <p className="text-2xl font-bold text-yellow-400">{orders.length}</p>
              </div>
            </div>
          </div>
        )
      case 'products':
        console.log('Rendering ProductManagement with products:', products.length, 'type:', typeof products, 'isArray:', Array.isArray(products))
        return <ProductManagement initialProducts={products} categories={categories} />
      case 'categories':
        return <CategoryManagement />
      case 'currencies':
        return <CurrenciesAdmin />
      case 'users':
        return <UserManagement />
      case 'orders':
        return <OrderManagement />
      case 'homepage':
        return <HomepageManagement />
      default:
        return <div className="p-6 bg-gray-800 rounded-lg">اختر تبويب من القائمة</div>
    }
  }





  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
          لوحة التحكم الإدارية
        </h1>

        {/* Simple Tab Navigation */}
        <div className="flex flex-wrap gap-2 mb-8">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? "bg-purple-600 text-white"
                    : "bg-gray-800 text-gray-300 hover:bg-gray-700"
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            )
          })}
        </div>

        {/* Tab Content */}
        <div className="bg-gray-800 rounded-lg p-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  )
}
