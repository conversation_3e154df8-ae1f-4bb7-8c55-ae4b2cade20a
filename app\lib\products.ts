import { supabase } from './supabase'
import { validateData, sanitizeObject } from './validations'
import { z } from 'zod'
import type { Product, Package, CustomField, DropdownOption } from '../types'

// Base product schema without refinements
const baseProductSchema = z.object({
  id: z.string().optional(),
  tenant_id: z.string().uuid('معرف المستأجر غير صالح').optional(),
  title: z.string()
    .min(1, 'عنوان المنتج مطلوب')
    .max(200, 'عنوان المنتج لا يمكن أن يزيد عن 200 حرف'),
  slug: z.string()
    .min(1, 'الرابط المختصر مطلوب')
    .max(200, 'الرابط المختصر لا يمكن أن يزيد عن 200 حرف')
    .regex(/^[a-z0-9\-]+$/, 'الرابط المختصر يجب أن يحتوي على أحرف صغيرة وأرقام وشرطات فقط'),
  description: z.string()
    .min(1, 'وصف المنتج مطلوب')
    .max(2000, 'وصف المنتج لا يمكن أن يزيد عن 2000 حرف'),
  category_id: z.string()
    .uuid('معرف الفئة غير صالح')
    .min(1, 'فئة المنتج مطلوبة'),
  cover_image: z.string()
    .url('رابط الصورة غير صالح')
    .optional(),
  tags: z.array(z.string().max(50, 'الوسم لا يمكن أن يزيد عن 50 حرف')).optional(),
  featured: z.boolean().optional(),

  // Pricing fields (optional, used when no packages)
  original_price: z.number()
    .positive('السعر الأصلي يجب أن يكون أكبر من صفر')
    .max(999999, 'السعر الأصلي كبير جداً')
    .optional(),
  user_price: z.number()
    .positive('سعر المستخدم يجب أن يكون أكبر من صفر')
    .max(999999, 'سعر المستخدم كبير جداً')
    .optional(),
  discount_price: z.number()
    .positive('سعر الخصم يجب أن يكون أكبر من صفر')
    .max(999999, 'سعر الخصم كبير جداً')
    .optional(),
  distributor_price: z.number()
    .positive('سعر الموزع يجب أن يكون أكبر من صفر')
    .max(999999, 'سعر الموزع كبير جداً')
    .optional(),

  // Inventory fields (for products without packages)
  manual_quantity: z.number().int().min(0, 'الكمية اليدوية لا يمكن أن تكون سالبة').optional(),
  track_inventory: z.boolean().optional(),
  unlimited_stock: z.boolean().optional(),
  has_digital_codes: z.boolean().optional()
})

// Product validation schema with refinements
export const productSchema = baseProductSchema.refine((data) => {
  // Pricing validation rules
  if (data.original_price && data.user_price) {
    if (data.user_price <= data.original_price) {
      return false // user_price must be > original_price
    }
  }

  if (data.discount_price && data.user_price && data.original_price) {
    if (data.discount_price >= data.user_price || data.discount_price <= data.original_price) {
      return false // original_price < discount_price < user_price
    }
  }

  if (data.distributor_price && data.original_price && data.user_price) {
    if (data.distributor_price <= data.original_price || data.distributor_price >= data.user_price) {
      return false // original_price < distributor_price < user_price
    }
  }

  return true
}, {
  message: 'أسعار المنتج لا تتبع القواعد المطلوبة'
})

// Base package schema without refinements
const basePackageSchema = z.object({
  id: z.string().optional(),
  tenant_id: z.string().uuid().optional(),
  product_id: z.string().uuid('معرف المنتج غير صالح'),
  name: z.string()
    .min(1, 'اسم الحزمة مطلوب')
    .max(200, 'اسم الحزمة لا يمكن أن يزيد عن 200 حرف'),
  description: z.string()
    .max(1000, 'وصف الحزمة لا يمكن أن يزيد عن 1000 حرف')
    .optional(),

  // Required pricing fields
  original_price: z.number()
    .positive('السعر الأصلي مطلوب ويجب أن يكون أكبر من صفر')
    .max(999999, 'السعر الأصلي كبير جداً'),
  user_price: z.number()
    .positive('سعر المستخدم مطلوب ويجب أن يكون أكبر من صفر')
    .max(999999, 'سعر المستخدم كبير جداً'),
  discount_price: z.number()
    .positive('سعر الخصم يجب أن يكون أكبر من صفر')
    .max(999999, 'سعر الخصم كبير جداً')
    .optional(),
  distributor_price: z.number()
    .positive('سعر الموزع يجب أن يكون أكبر من صفر')
    .max(999999, 'سعر الموزع كبير جداً')
    .optional(),

  digital_codes: z.string().optional(),
  image: z.string().url('رابط الصورة غير صالح').optional(),

  // Inventory fields
  manual_quantity: z.number().int().min(0, 'الكمية اليدوية لا يمكن أن تكون سالبة').optional(),
  track_inventory: z.boolean().optional(),
  unlimited_stock: z.boolean().optional(),
  has_digital_codes: z.boolean().optional()
})

// Package validation schema with refinements
export const packageSchema = basePackageSchema.refine((data) => {
  // Same pricing validation rules as product
  if (data.user_price <= data.original_price) {
    return false
  }

  if (data.discount_price) {
    if (data.discount_price >= data.user_price || data.discount_price <= data.original_price) {
      return false
    }
  }

  if (data.distributor_price) {
    if (data.distributor_price <= data.original_price || data.distributor_price >= data.user_price) {
      return false
    }
  }

  return true
}, {
  message: 'أسعار الحزمة لا تتبع القواعد المطلوبة'
})

// Base custom field schema without refinements
const baseCustomFieldSchema = z.object({
  id: z.string().optional(),
  tenant_id: z.string().uuid().optional(),
  product_id: z.string().uuid('معرف المنتج غير صالح'),
  label: z.string()
    .min(1, 'تسمية الحقل مطلوبة')
    .max(100, 'تسمية الحقل لا يمكن أن تزيد عن 100 حرف'),
  field_type: z.enum(['text', 'dropdown'], {
    errorMap: () => ({ message: 'نوع الحقل يجب أن يكون نص أو قائمة منسدلة' })
  }),
  required: z.boolean(),
  placeholder: z.string()
    .max(200, 'النص التوضيحي لا يمكن أن يزيد عن 200 حرف')
    .optional(),
  description: z.string()
    .max(500, 'وصف الحقل لا يمكن أن يزيد عن 500 حرف')
    .optional(),
  field_order: z.number().int().min(0),
  options: z.array(z.object({
    label: z.string().min(1, 'تسمية الخيار مطلوبة').max(100),
    value: z.string().min(1, 'قيمة الخيار مطلوبة').max(100)
  })).optional()
})

// Custom field validation schema with refinements
export const customFieldSchema = baseCustomFieldSchema.refine((data) => {
  // Dropdown fields must have at least one option
  if (data.field_type === 'dropdown') {
    return data.options && data.options.length > 0
  }
  return true
}, {
  message: 'القائمة المنسدلة يجب أن تحتوي على خيار واحد على الأقل'
})

export const productCreateSchema = baseProductSchema.omit({ id: true, tenant_id: true })
export const productUpdateSchema = baseProductSchema.partial().omit({ id: true, tenant_id: true })
export const packageCreateSchema = basePackageSchema.omit({ id: true, tenant_id: true })
export const packageUpdateSchema = basePackageSchema.partial().omit({ id: true, tenant_id: true })
export const customFieldCreateSchema = baseCustomFieldSchema.omit({ id: true, tenant_id: true })
export const customFieldUpdateSchema = baseCustomFieldSchema.partial().omit({ id: true, tenant_id: true })

// Utility functions for profit calculations
export function calculateProfits(pricing: {
  original_price: number
  user_price: number
  discount_price?: number
  distributor_price?: number
}) {
  // BUSINESS LOGIC: Customer Purchase Price Priority
  // When discount_price is set, customers buy at discount_price (NOT user_price)
  // This means the actual selling price is discount_price when available
  // For user-facing pages and order management: always use effectivePrice for customer transactions
  const effectivePrice = pricing.discount_price || pricing.user_price

  // PROFIT CALCULATION: Based on actual selling price
  // userProfit = what we earn from regular customers (after discount if applicable)
  // When discount is active: profit = discount_price - original_price
  // When no discount: profit = user_price - original_price
  const userProfit = effectivePrice - pricing.original_price

  // DISTRIBUTOR PROFIT: Always calculated from distributor_price (independent of discounts)
  // Distributors get special pricing regardless of customer discounts
  const distributorProfit = pricing.distributor_price ? pricing.distributor_price - pricing.original_price : 0

  // DISCOUNT PERCENTAGE: Shows how much discount customers get from regular price
  // Formula: ((regular_price - discounted_price) / regular_price) * 100
  // This is for display purposes to show customers the savings percentage
  const discountPercentage = pricing.discount_price
    ? ((pricing.user_price - pricing.discount_price) / pricing.user_price) * 100
    : 0

  return {
    userProfit,        // Actual profit from customer purchases (considers discount)
    distributorProfit, // Profit from distributor sales
    discountPercentage: Math.round(discountPercentage * 100) / 100 // Discount % for marketing display
  }
}

// Generate slug from title
export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\u0600-\u06FFa-z0-9\s\-]/g, '') // Keep Arabic, English, numbers, spaces, hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
}

// Validate pricing rules - BUSINESS LOGIC ENFORCEMENT
// Price hierarchy: original_price < discount_price < distributor_price < user_price
// This ensures profitability at all levels while maintaining logical pricing structure
export function validatePricingRules(pricing: {
  original_price: number
  user_price: number
  discount_price?: number
  distributor_price?: number
}): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  // Base rule: user_price must be higher than cost (original_price) to ensure profit
  if (pricing.user_price <= pricing.original_price) {
    errors.push('سعر المستخدم يجب أن يكون أكبر من السعر الأصلي')
  }

  // Discount validation: discount must be between original_price and user_price
  // This ensures: 1) We still make profit, 2) It's actually a discount for customers
  if (pricing.discount_price) {
    if (pricing.discount_price >= pricing.user_price) {
      errors.push('سعر الخصم يجب أن يكون أقل من سعر المستخدم')
    }
    if (pricing.discount_price <= pricing.original_price) {
      errors.push('سعر الخصم يجب أن يكون أكبر من السعر الأصلي')
    }
  }

  // Distributor validation: distributor gets better price than regular customers but we still profit
  // distributor_price should be between original_price and user_price
  if (pricing.distributor_price) {
    if (pricing.distributor_price <= pricing.original_price) {
      errors.push('سعر الموزع يجب أن يكون أكبر من السعر الأصلي')
    }
    if (pricing.distributor_price >= pricing.user_price) {
      errors.push('سعر الموزع يجب أن يكون أقل من سعر المستخدم')
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}
