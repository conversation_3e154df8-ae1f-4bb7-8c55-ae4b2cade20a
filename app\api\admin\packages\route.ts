import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'
import { rateLimit } from '../../../lib/rate-limit'
import { 
  packageCreateSchema, 
  validatePricingRules 
} from '../../../lib/products'

// GET /api/admin/packages - Get packages for a product
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'ملف المستخدم غير موجود' }, { status: 404 })
    }

    // Parse query parameters
    const url = new URL(request.url)
    const productId = url.searchParams.get('product_id')

    if (!productId) {
      return NextResponse.json({ 
        success: false,
        error: 'معرف المنتج مطلوب' 
      }, { status: 400 })
    }

    // Verify product exists and belongs to tenant
    const { data: product } = await supabase
      .from('products')
      .select('id')
      .eq('id', productId)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!product) {
      return NextResponse.json({ 
        success: false,
        error: 'المنتج غير موجود' 
      }, { status: 404 })
    }

    // Get packages for this product
    const { data: packages, error } = await supabase
      .from('packages')
      .select('*')
      .eq('product_id', productId)
      .eq('tenant_id', profile.tenant_id)
      .order('created_at', { ascending: true })

    if (error) {
      console.error('Database error fetching packages:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في جلب الحزم',
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: packages || []
    })

  } catch (error) {
    console.error('Error in packages GET:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// POST /api/admin/packages - Create new package
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    const body = await request.json()
    
    // Validate input
    const validation = packageCreateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        success: false,
        error: 'بيانات غير صالحة',
        details: validation.error.errors.map(e => e.message)
      }, { status: 400 })
    }

    const validatedData = validation.data

    // Validate pricing rules
    const pricingValidation = validatePricingRules({
      original_price: validatedData.original_price,
      user_price: validatedData.user_price,
      discount_price: validatedData.discount_price,
      distributor_price: validatedData.distributor_price
    })

    if (!pricingValidation.valid) {
      return NextResponse.json({
        success: false,
        error: 'أخطاء في التسعير',
        details: pricingValidation.errors
      }, { status: 400 })
    }

    // Verify product exists and belongs to tenant
    const { data: product } = await supabase
      .from('products')
      .select('id')
      .eq('id', validatedData.product_id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!product) {
      return NextResponse.json({ 
        success: false,
        error: 'المنتج المحدد غير موجود' 
      }, { status: 400 })
    }

    // Process digital codes if provided
    let hasDigitalCodes = false
    let digitalCodesArray: string[] = []
    
    if (validatedData.digital_codes && validatedData.digital_codes.trim()) {
      digitalCodesArray = validatedData.digital_codes
        .split('\n')
        .map(code => code.trim())
        .filter(code => code.length > 0)
      
      hasDigitalCodes = digitalCodesArray.length > 0
    }

    // Insert new package
    const { data: packageData, error: packageError } = await supabase
      .from('packages')
      .insert({
        ...validatedData,
        tenant_id: profile.tenant_id,
        has_digital_codes: validatedData.has_digital_codes || hasDigitalCodes,
        track_inventory: validatedData.track_inventory ?? (!hasDigitalCodes),
        unlimited_stock: validatedData.unlimited_stock ?? (!hasDigitalCodes),
        manual_quantity: validatedData.manual_quantity ?? (hasDigitalCodes ? 0 : 999999)
      })
      .select()
      .single()

    if (packageError) {
      console.error('Error creating package:', packageError)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في إنشاء الحزمة' 
      }, { status: 500 })
    }

    // Insert digital codes if provided
    if (hasDigitalCodes && digitalCodesArray.length > 0) {
      const digitalCodeInserts = digitalCodesArray.map(code => ({
        tenant_id: profile.tenant_id,
        package_id: packageData.id,
        key_encrypted: code, // In production, this should be encrypted
        used: false,
        viewed_count: 0
      }))

      const { error: codesError } = await supabase
        .from('digital_codes')
        .insert(digitalCodeInserts)

      if (codesError) {
        console.error('Error inserting digital codes:', codesError)
        // Don't fail the package creation, just log the error
        console.warn('Package created but digital codes failed to insert')
      }
    }

    return NextResponse.json({ 
      success: true,
      data: packageData 
    }, { status: 201 })

  } catch (error) {
    console.error('Error in packages POST:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}
