import { useState, useEffect, useCallback } from 'react'
import { useTenant } from '../../contexts/TenantContext'

export interface PaginationData {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface UseAdminCRUDParams {
  entityType: string
  apiEndpoint: string
  initialLimit?: number
}

export interface UseAdminCRUDReturn<T> {
  items: T[]
  loading: boolean
  actionLoading: string | null
  pagination: PaginationData
  filters: Record<string, any>
  fetchItems: (page?: number, filters?: Record<string, any>, useCache?: boolean) => Promise<void>
  goToPage: (page: number) => void
  nextPage: () => void
  prevPage: () => void
  updateFilters: (newFilters: Record<string, any>) => void
}

export function useAdminCRUD<T extends { id: string }>({
  entityType,
  apiEndpoint,
  initialLimit = 10
}: UseAdminCRUDParams): UseAdminCRUDReturn<T> {
  const { tenant } = useTenant()
  
  // State management
  const [items, setItems] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: initialLimit,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })
  const [filters, setFilters] = useState<Record<string, any>>({})

  // Fetch items from API
  const fetchItems = useCallback(async (
    page: number = pagination.page,
    currentFilters: Record<string, any> = filters,
    useCache: boolean = true
  ) => {
    if (!tenant) return

    try {
      setLoading(true)
      
      // Build query parameters
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        tenant_id: tenant.id
      })

      // Add filters to params
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString())
        }
      })

      const response = await fetch(`${apiEndpoint}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ${entityType}`)
      }
      
      const data = await response.json()
      
      // Handle different response formats
      const newItems = data[entityType] || data.data || data.items || []
      const newPagination = data.pagination || {
        page,
        limit: pagination.limit,
        total: newItems.length,
        totalPages: Math.ceil(newItems.length / pagination.limit),
        hasNext: false,
        hasPrev: false
      }
      
      setItems(newItems)
      setPagination(newPagination)
      
    } catch (error) {
      console.error(`Error fetching ${entityType}:`, error)
      setItems([])
      setPagination(prev => ({
        ...prev,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      }))
    } finally {
      setLoading(false)
    }
  }, [tenant, entityType, apiEndpoint, pagination.limit, filters])

  // Navigation functions
  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= pagination.totalPages) {
      fetchItems(page, filters, false)
    }
  }, [fetchItems, filters, pagination.totalPages])

  const nextPage = useCallback(() => {
    if (pagination.hasNext) {
      goToPage(pagination.page + 1)
    }
  }, [goToPage, pagination.hasNext, pagination.page])

  const prevPage = useCallback(() => {
    if (pagination.hasPrev) {
      goToPage(pagination.page - 1)
    }
  }, [goToPage, pagination.hasPrev, pagination.page])

  // Filter management
  const updateFilters = useCallback((newFilters: Record<string, any>) => {
    setFilters(newFilters)
    // Reset to page 1 when filters change
    fetchItems(1, newFilters, false)
  }, [fetchItems])

  // Initial load
  useEffect(() => {
    if (tenant) {
      fetchItems(1, filters, false)
    }
  }, [tenant]) // Only depend on tenant to avoid infinite loops

  return {
    items,
    loading,
    actionLoading,
    pagination,
    filters,
    fetchItems,
    goToPage,
    nextPage,
    prevPage,
    updateFilters
  }
}
