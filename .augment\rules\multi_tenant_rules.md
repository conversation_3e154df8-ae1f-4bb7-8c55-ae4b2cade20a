---
type: "manual"
---

# Global Rules – Multi‑Tenant SaaS Platform
_Apply these rules automatically to every PR review, code‑generation request, or documentation task. They can be referenced explicitly with **@multi-tenant.rules.md**._

---
## 1 · Database & Security
1. **Every new table _must_ include a `tenant_id UUID REFERENCES tenants(id)` column.**  No exceptions.
2. **Row‑Level Security (RLS) policies _must_ restrict access to `tenant_id = current_setting('request.jwt.claim.tenant_id')`.**  Read/write queries are denied if the tenant check fails.
3. **Primary keys are UUID v4 via `gen_random_uuid()`; never use serial integers.**
4. **Use composite indexes on (`tenant_id`, frequently‑queried‑column) to keep per‑tenant performance flat.**
5. **Data migrations must back‑fill `tenant_id` with `'main'` when upgrading the legacy single‑tenant data.**

---
## 2 · API & Middleware
1. **All backend route handlers must resolve `tenant_id` at the edge** (via domain→tenant lookup or JWT claim) **_before_ touching the database.**
2. **Never trust client‑supplied `tenant_id` fields; derive them server‑side.**
3. **If the request lacks a valid tenant mapping, return HTTP 404 (tenant not found) — never leak another tenant’s data.**
4. **Expose feature flags per tenant via `/api/tenants/:id/features`; features are disabled by default.**

---
## 3 · Front‑End
1. **All data‑fetch hooks must include `tenant_id` in the filter or rely on server‑side RLS.**
2. **Tenant theme variables (`primaryColor`, `logoUrl`, etc.) are fetched _once_ at boot and stored in global context.**
3. **UI must hide or gray‑out unavailable features based on tenant feature flags.**
4. **Do not hard‑code tenant‑specific colors, copy, or assets in shared components.**

---
## 4 · Documentation Policy
1. **Every PR that touches schema or business logic _must_ update the relevant docs in `/docs/`.**  CI fails if docs missing.
2. **Remove superseded single‑tenant docs immediately; no dead paragraphs.**
3. **Docs for new features must specify whether the feature is opt‑in, opt‑out, or premium per tenant.**

---
## 5 · Feature Flags & Plans
1. **`tenant_features` table is the single source of truth for feature availability.**
2. **Flags are evaluated server‑side; front‑end never trusts its own cache beyond current session.**
3. **Plan tiers (`basic`, `pro`, `enterprise`) are enforced via database views that union allowed features.**

---
## 6 · Testing & CI
1. **Every new endpoint must include unit tests for tenant isolation** (attempt cross‑tenant access ➔ expect 403/404).
2. **E2E test suite must spin up _at least two tenants_ to verify isolation and theming.**
3. **Performance regression budget:** <300 ms P95 for tenant‑scoped list endpoints under 1k rows.

---
## 7 · Miscellaneous
1. **Default tenant row (`id = 'main'`) stays reserved for internal admin/testing; never delete.**
2. **Cron jobs and background workers _must_ run in tenant‑aware loops or explicitly document why global scope is safe.**
3. **Never log full JWTs; log only their `sub` and `tenant_id` claims when debugging.**

---
_If any rule conflicts with a domain‑specific requirement, open an `@architect_review` thread before merging._

