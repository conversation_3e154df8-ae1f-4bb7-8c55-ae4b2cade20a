import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'
import { rateLimit } from '../../../../lib/rate-limit'
import { customFieldUpdateSchema } from '../../../../lib/products'

// GET /api/admin/custom-fields/[id] - Get single custom field
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'ملف المستخدم غير موجود' }, { status: 404 })
    }

    // Get custom field
    const { data: customField, error } = await supabase
      .from('custom_fields')
      .select('*')
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (error || !customField) {
      return NextResponse.json({ 
        success: false,
        error: 'الحقل المخصص غير موجود' 
      }, { status: 404 })
    }

    return NextResponse.json({ 
      success: true,
      data: customField 
    })

  } catch (error) {
    console.error('Error in custom field GET:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// PUT /api/admin/custom-fields/[id] - Update custom field
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    const body = await request.json()
    
    // Validate input
    const validation = customFieldUpdateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        success: false,
        error: 'بيانات غير صالحة',
        details: validation.error.errors.map(e => e.message)
      }, { status: 400 })
    }

    const validatedData = validation.data

    // Check if custom field exists and belongs to tenant
    const { data: existingField } = await supabase
      .from('custom_fields')
      .select('id, product_id')
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!existingField) {
      return NextResponse.json({
        success: false,
        error: 'الحقل المخصص غير موجود'
      }, { status: 404 })
    }

    // Update custom field
    const { data: customField, error } = await supabase
      .from('custom_fields')
      .update({
        ...validatedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating custom field:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في تحديث الحقل المخصص' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      data: customField 
    })

  } catch (error) {
    console.error('Error in custom field PUT:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// DELETE /api/admin/custom-fields/[id] - Delete custom field
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    // Check if custom field exists and belongs to tenant
    const { data: customField } = await supabase
      .from('custom_fields')
      .select('id, product_id, field_order')
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!customField) {
      return NextResponse.json({
        success: false,
        error: 'الحقل المخصص غير موجود'
      }, { status: 404 })
    }

    // Delete custom field
    const { error } = await supabase
      .from('custom_fields')
      .delete()
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)

    if (error) {
      console.error('Error deleting custom field:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في حذف الحقل المخصص' 
      }, { status: 500 })
    }

    // Reorder remaining fields
    const { data: remainingFields } = await supabase
      .from('custom_fields')
      .select('id, field_order')
      .eq('product_id', customField.product_id)
      .eq('tenant_id', profile.tenant_id)
      .gt('field_order', customField.field_order)
      .order('field_order', { ascending: true })

    if (remainingFields && remainingFields.length > 0) {
      const updates = remainingFields.map((field, index) => 
        supabase
          .from('custom_fields')
          .update({ field_order: customField.field_order + index })
          .eq('id', field.id)
          .eq('tenant_id', profile.tenant_id)
      )

      await Promise.all(updates)
    }

    return NextResponse.json({ 
      success: true,
      message: 'تم حذف الحقل المخصص بنجاح' 
    })

  } catch (error) {
    console.error('Error in custom field DELETE:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}
