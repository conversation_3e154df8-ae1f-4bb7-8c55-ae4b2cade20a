"use client"

import React, { useState, useEffect } from 'react'
import { Activity, Clock, Database, Zap } from 'lucide-react'
import { useCache } from '../lib/cache'

interface PerformanceMetrics {
  pageLoadTime: number
  apiResponseTime: number
  cacheHitRate: number
  totalRequests: number
  errorRate: number
}

interface PerformanceMonitorProps {
  showDetails?: boolean
  className?: string
}

export default function PerformanceMonitor({ 
  showDetails = false, 
  className = '' 
}: PerformanceMonitorProps) {
  const cache = useCache()
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    pageLoadTime: 0,
    apiResponseTime: 0,
    cacheHitRate: 0,
    totalRequests: 0,
    errorRate: 0
  })
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only show in development or for admin users
    const isDev = process.env.NODE_ENV === 'development'
    const isAdmin = localStorage.getItem('user_role') === 'admin'
    setIsVisible(isDev || isAdmin)

    if (isDev || isAdmin) {
      updateMetrics()
      const interval = setInterval(updateMetrics, 5000) // Update every 5 seconds
      return () => clearInterval(interval)
    }
  }, [])

  const updateMetrics = () => {
    try {
      // Get cache statistics
      const cacheStats = cache.getStats()
      
      // Get performance metrics from browser
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      const pageLoadTime = navigation ? navigation.loadEventEnd - navigation.fetchStart : 0

      // Get API response times from performance entries
      const apiEntries = performance.getEntriesByType('resource')
        .filter(entry => entry.name.includes('/api/'))
      
      const avgApiResponseTime = apiEntries.length > 0
        ? apiEntries.reduce((sum, entry) => sum + entry.duration, 0) / apiEntries.length
        : 0

      setMetrics({
        pageLoadTime: Math.round(pageLoadTime),
        apiResponseTime: Math.round(avgApiResponseTime),
        cacheHitRate: 0, // Simplified cache doesn't track hit rate
        totalRequests: cacheStats.keys,
        errorRate: 0 // Would need error tracking implementation
      })
    } catch (error) {
      console.warn('Performance monitoring error:', error)
    }
  }

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-400'
    if (value <= thresholds.warning) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getCacheColor = (hitRate: number) => {
    if (hitRate >= 80) return 'text-green-400'
    if (hitRate >= 60) return 'text-yellow-400'
    return 'text-red-400'
  }

  if (!isVisible) return null

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
      {showDetails ? (
        <div className="bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg p-4 min-w-[300px] shadow-xl">
          <div className="flex items-center gap-2 mb-3">
            <Activity className="w-4 h-4 text-blue-400" />
            <h3 className="text-sm font-semibold text-white">Performance Monitor</h3>
          </div>
          
          <div className="space-y-2 text-xs">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Clock className="w-3 h-3 text-gray-400" />
                <span className="text-gray-300">Page Load</span>
              </div>
              <span className={getPerformanceColor(metrics.pageLoadTime, { good: 1000, warning: 3000 })}>
                {metrics.pageLoadTime}ms
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap className="w-3 h-3 text-gray-400" />
                <span className="text-gray-300">API Response</span>
              </div>
              <span className={getPerformanceColor(metrics.apiResponseTime, { good: 200, warning: 500 })}>
                {metrics.apiResponseTime}ms
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database className="w-3 h-3 text-gray-400" />
                <span className="text-gray-300">Cache Hit Rate</span>
              </div>
              <span className={getCacheColor(metrics.cacheHitRate)}>
                {metrics.cacheHitRate.toFixed(1)}%
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-300">Total Requests</span>
              <span className="text-gray-300">{metrics.totalRequests}</span>
            </div>

            <div className="pt-2 border-t border-gray-700">
              <button
                onClick={() => {
                  cache.getStats()
                  updateMetrics()
                }}
                className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
              >
                Refresh Metrics
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gray-900/90 backdrop-blur-sm border border-gray-700 rounded-full p-2 shadow-lg">
          <div className="flex items-center gap-2">
            <Activity className="w-4 h-4 text-blue-400" />
            <div className="flex items-center gap-1 text-xs">
              <span className={getPerformanceColor(metrics.pageLoadTime, { good: 1000, warning: 3000 })}>
                {metrics.pageLoadTime}ms
              </span>
              <span className="text-gray-500">|</span>
              <span className={getCacheColor(metrics.cacheHitRate)}>
                {metrics.cacheHitRate.toFixed(0)}%
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Hook for tracking API performance
 */
export function useApiPerformance() {
  const trackApiCall = (endpoint: string, startTime: number) => {
    const duration = Date.now() - startTime
    
    // Store performance data
    const perfData = {
      endpoint,
      duration,
      timestamp: Date.now()
    }
    
    // Store in sessionStorage for performance monitoring
    const existingData = JSON.parse(sessionStorage.getItem('api_performance') || '[]')
    existingData.push(perfData)
    
    // Keep only last 100 entries
    if (existingData.length > 100) {
      existingData.splice(0, existingData.length - 100)
    }
    
    sessionStorage.setItem('api_performance', JSON.stringify(existingData))
    
    // Log slow API calls
    if (duration > 1000) {
      console.warn(`Slow API call detected: ${endpoint} took ${duration}ms`)
    }
  }

  const getApiStats = () => {
    const data = JSON.parse(sessionStorage.getItem('api_performance') || '[]')
    
    if (data.length === 0) return null
    
    const totalDuration = data.reduce((sum: number, entry: any) => sum + entry.duration, 0)
    const avgDuration = totalDuration / data.length
    const slowCalls = data.filter((entry: any) => entry.duration > 1000).length
    
    return {
      totalCalls: data.length,
      averageDuration: Math.round(avgDuration),
      slowCalls,
      slowCallRate: (slowCalls / data.length) * 100
    }
  }

  return { trackApiCall, getApiStats }
}

/**
 * Higher-order component for performance monitoring
 */
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) {
  return function PerformanceMonitoredComponent(props: P) {
    useEffect(() => {
      const startTime = Date.now()
      
      return () => {
        const renderTime = Date.now() - startTime
        if (renderTime > 100) {
          console.warn(`Slow component render: ${componentName} took ${renderTime}ms`)
        }
      }
    }, [])

    return <WrappedComponent {...props} />
  }
}
