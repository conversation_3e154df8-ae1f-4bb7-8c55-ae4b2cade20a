"use client"

import { useState, useEffect } from 'react'
import { Upload, X, Tag as TagIcon } from 'lucide-react'
import { generateSlug } from '../../../lib/products'
import type { Category } from '../../../types'

interface BasicInfoTabProps {
  formData: {
    title: string
    slug: string
    description: string
    category_id: string
    cover_image: string
    tags: string[]
    featured: boolean
  }
  onChange: (data: Partial<BasicInfoTabProps['formData']>) => void
  errors: Record<string, string>
  categories: Category[]
  loading?: boolean
}

export default function BasicInfoTab({ 
  formData, 
  onChange, 
  errors, 
  categories,
  loading = false 
}: BasicInfoTabProps) {
  const [tagInput, setTagInput] = useState('')

  // Auto-generate slug when title changes
  useEffect(() => {
    if (formData.title && !formData.slug) {
      onChange({ slug: generateSlug(formData.title) })
    }
  }, [formData.title, formData.slug, onChange])

  const handleAddTag = () => {
    const tag = tagInput.trim()
    if (tag && !formData.tags.includes(tag)) {
      onChange({ tags: [...formData.tags, tag] })
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    onChange({ tags: formData.tags.filter(tag => tag !== tagToRemove) })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  return (
    <div className="space-y-6">
      {/* Title and Slug Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-200">
            عنوان المنتج <span className="text-red-400">*</span>
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => onChange({ title: e.target.value })}
            className={`w-full px-4 py-3 rounded-lg border ${
              errors.title
                ? 'border-red-500 focus:border-red-500'
                : 'border-gray-600 focus:border-purple-500'
            } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
            placeholder="أدخل عنوان المنتج..."
            disabled={loading}
          />
          {errors.title && (
            <p className="text-red-400 text-sm mt-1">{errors.title}</p>
          )}
        </div>

        {/* Slug */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-200">
            الرابط المختصر <span className="text-red-400">*</span>
          </label>
          <input
            type="text"
            value={formData.slug}
            onChange={(e) => onChange({ slug: e.target.value })}
            className={`w-full px-4 py-3 rounded-lg border ${
              errors.slug
                ? 'border-red-500 focus:border-red-500'
                : 'border-gray-600 focus:border-purple-500'
            } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
            placeholder="product-slug"
            disabled={loading}
          />
          {errors.slug && (
            <p className="text-red-400 text-sm mt-1">{errors.slug}</p>
          )}
          <p className="text-gray-400 text-xs mt-1">
            يجب أن يحتوي على أحرف صغيرة وأرقام وشرطات فقط
          </p>
        </div>
      </div>

      {/* Category and Featured Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        {/* Category */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-200">
            الفئة <span className="text-red-400">*</span>
          </label>
          <select
            value={formData.category_id}
            onChange={(e) => onChange({ category_id: e.target.value })}
            className={`w-full px-4 py-3 rounded-lg border ${
              errors.category_id
                ? 'border-red-500 focus:border-red-500'
                : 'border-gray-600 focus:border-purple-500'
            } bg-gray-700/50 backdrop-blur-sm text-white focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
            disabled={loading}
          >
            <option value="">اختر الفئة...</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name || category.slug}
              </option>
            ))}
          </select>
          {errors.category_id && (
            <p className="text-red-400 text-sm mt-1">{errors.category_id}</p>
          )}
        </div>

        {/* Featured Toggle */}
        <div className="flex items-center justify-center lg:justify-start">
          <div className="bg-gray-800/50 rounded-lg p-4 w-full">
            <label className="flex items-center gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.featured}
                onChange={(e) => onChange({ featured: e.target.checked })}
                disabled={loading}
                className="w-5 h-5 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500 focus:ring-2"
              />
              <div>
                <span className="text-sm font-medium text-gray-200">منتج مميز</span>
                <p className="text-xs text-gray-400">سيظهر في القسم المميز</p>
              </div>
            </label>
          </div>
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-200">
          وصف المنتج <span className="text-red-400">*</span>
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => onChange({ description: e.target.value })}
          rows={4}
          className={`w-full px-4 py-3 rounded-lg border ${
            errors.description
              ? 'border-red-500 focus:border-red-500'
              : 'border-gray-600 focus:border-purple-500'
          } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all resize-none`}
          placeholder="أدخل وصف مفصل للمنتج..."
          disabled={loading}
        />
        {errors.description && (
          <p className="text-red-400 text-sm mt-1">{errors.description}</p>
        )}
      </div>

      {/* Cover Image */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-200">
          صورة الغلاف
        </label>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="space-y-3">
            <input
              type="url"
              value={formData.cover_image}
              onChange={(e) => onChange({ cover_image: e.target.value })}
              className={`w-full px-4 py-3 rounded-lg border ${
                errors.cover_image
                  ? 'border-red-500 focus:border-red-500'
                  : 'border-gray-600 focus:border-purple-500'
              } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
              placeholder="https://example.com/image.jpg"
              disabled={loading}
            />
            {errors.cover_image && (
              <p className="text-red-400 text-sm">{errors.cover_image}</p>
            )}
            <p className="text-gray-400 text-xs">
              أدخل رابط صورة صالح أو اتركه فارغاً
            </p>
          </div>
          {formData.cover_image && (
            <div className="relative w-full h-48 rounded-lg overflow-hidden bg-gray-800 border border-gray-700">
              <img
                src={formData.cover_image}
                alt="معاينة الصورة"
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement
                  target.style.display = 'none'
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Tags */}
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-200">
          الوسوم
        </label>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-2">
            <input
              type="text"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1 px-4 py-3 rounded-lg border border-gray-600 focus:border-purple-500 bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all"
              placeholder="أدخل وسم جديد..."
              disabled={loading}
            />
            <button
              type="button"
              onClick={handleAddTag}
              disabled={!tagInput.trim() || loading}
              className="px-6 py-3 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center justify-center gap-2 whitespace-nowrap"
            >
              <TagIcon className="w-4 h-4" />
              إضافة
            </button>
          </div>

          {formData.tags.length > 0 && (
            <div className="bg-gray-800/30 rounded-lg p-4 border border-gray-700/50">
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-2 px-3 py-2 bg-purple-600/20 text-purple-300 rounded-lg text-sm border border-purple-500/20"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      disabled={loading}
                      className="hover:text-red-400 transition-colors p-0.5 hover:bg-red-500/20 rounded"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}

          <p className="text-gray-400 text-xs">
            الوسوم تساعد في تصنيف وإيجاد المنتجات بسهولة
          </p>
        </div>
      </div>
    </div>
  )
}
