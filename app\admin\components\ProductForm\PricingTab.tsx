"use client"

import { useMemo, useState, useEffect } from 'react'
import { DollarSign, TrendingUp, Percent, Package, Code, Hash } from 'lucide-react'
import { calculateProfits, validatePricingRules } from '../../../lib/products'

interface PricingTabProps {
  formData: {
    original_price: number
    user_price: number
    discount_price?: number
    distributor_price?: number
    // Inventory fields for products without packages
    manual_quantity?: number
    track_inventory?: boolean
    unlimited_stock?: boolean
    has_digital_codes?: boolean
  }
  onChange: (data: Partial<PricingTabProps['formData']>) => void
  errors: Record<string, string>
  loading?: boolean
  hasPackages?: boolean
}

export default function PricingTab({
  formData,
  onChange,
  errors,
  loading = false,
  hasPackages = false
}: PricingTabProps) {

  // State for digital codes input
  const [digitalCodesInput, setDigitalCodesInput] = useState('')

  // Calculate quantity from digital codes
  const calculateCodesQuantity = (codes: string) => {
    if (!codes.trim()) return 0
    return codes.trim().split('\n').filter(line => line.trim()).length
  }

  // Handle digital codes input change
  const handleDigitalCodesChange = (codes: string) => {
    setDigitalCodesInput(codes)
    const quantity = calculateCodesQuantity(codes)
    const hasDigitalCodes = quantity > 0

    onChange({
      has_digital_codes: hasDigitalCodes,
      manual_quantity: hasDigitalCodes ? quantity : formData.manual_quantity,
      track_inventory: hasDigitalCodes ? true : formData.track_inventory,
      unlimited_stock: hasDigitalCodes ? false : formData.unlimited_stock
    })
  }

  // Handle manual quantity vs unlimited stock mutual exclusivity
  const handleManualQuantityChange = (quantity: number) => {
    onChange({
      manual_quantity: quantity,
      unlimited_stock: quantity > 0 ? false : formData.unlimited_stock,
      track_inventory: quantity > 0 ? true : false
    })
  }

  const handleUnlimitedStockChange = (unlimited: boolean) => {
    onChange({
      unlimited_stock: unlimited,
      manual_quantity: unlimited ? 0 : formData.manual_quantity,
      track_inventory: unlimited ? false : (formData.manual_quantity || 0) > 0
    })
  }

  // Calculate profits in real-time
  const calculations = useMemo(() => {
    if (!formData.original_price || !formData.user_price) {
      return null
    }

    try {
      return calculateProfits({
        original_price: formData.original_price,
        user_price: formData.user_price,
        discount_price: formData.discount_price,
        distributor_price: formData.distributor_price
      })
    } catch {
      return null
    }
  }, [formData])

  // Validate pricing rules in real-time
  const validationErrors = useMemo(() => {
    if (!formData.original_price || !formData.user_price) {
      return []
    }

    const validation = validatePricingRules({
      original_price: formData.original_price,
      user_price: formData.user_price,
      discount_price: formData.discount_price,
      distributor_price: formData.distributor_price
    })

    return validation.errors
  }, [formData])

  // Don't show pricing tab if packages exist
  if (hasPackages) {
    return (
      <div className="text-center py-12">
        <DollarSign className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-300 mb-2">
          التسعير غير متاح
        </h3>
        <p className="text-gray-400">
          يتم إدارة التسعير من خلال الحزم عندما تكون موجودة
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Pricing Fields */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        {/* Original Price */}
        <div>
          <label className="block text-sm font-medium mb-2 text-gray-200">
            السعر الأصلي <span className="text-red-400">*</span>
          </label>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.original_price || ''}
              onChange={(e) => onChange({ original_price: parseFloat(e.target.value) || 0 })}
              className={`w-full pl-10 pr-4 py-3 rounded-lg border ${
                errors.original_price || validationErrors.length > 0
                  ? 'border-red-500 focus:border-red-500'
                  : 'border-gray-600 focus:border-purple-500'
              } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
              placeholder="0.00"
              disabled={loading}
            />
          </div>
          {errors.original_price && (
            <p className="text-red-400 text-sm mt-1">{errors.original_price}</p>
          )}
          <p className="text-gray-400 text-xs mt-1">
            التكلفة الأساسية للمنتج
          </p>
        </div>

        {/* User Price */}
        <div>
          <label className="block text-sm font-medium mb-2">
            سعر المستخدم <span className="text-red-400">*</span>
          </label>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.user_price || ''}
              onChange={(e) => onChange({ user_price: parseFloat(e.target.value) || 0 })}
              className={`w-full pl-10 pr-4 py-3 rounded-lg border ${
                errors.user_price || validationErrors.length > 0
                  ? 'border-red-500 focus:border-red-500' 
                  : 'border-gray-600 focus:border-purple-500'
              } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
              placeholder="0.00"
              disabled={loading}
            />
          </div>
          {errors.user_price && (
            <p className="text-red-400 text-sm mt-1">{errors.user_price}</p>
          )}
          <p className="text-gray-400 text-xs mt-1">
            السعر العادي للعملاء
          </p>
        </div>

        {/* Discount Price */}
        <div>
          <label className="block text-sm font-medium mb-2">
            سعر الخصم
          </label>
          <div className="relative">
            <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.discount_price || ''}
              onChange={(e) => onChange({ discount_price: parseFloat(e.target.value) || undefined })}
              className={`w-full pl-10 pr-4 py-3 rounded-lg border ${
                errors.discount_price || validationErrors.length > 0
                  ? 'border-red-500 focus:border-red-500' 
                  : 'border-gray-600 focus:border-purple-500'
              } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
              placeholder="0.00"
              disabled={loading}
            />
          </div>
          {errors.discount_price && (
            <p className="text-red-400 text-sm mt-1">{errors.discount_price}</p>
          )}
          <p className="text-gray-400 text-xs mt-1">
            السعر المخفض (اختياري)
          </p>
        </div>

        {/* Distributor Price */}
        <div>
          <label className="block text-sm font-medium mb-2">
            سعر الموزع
          </label>
          <div className="relative">
            <TrendingUp className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.distributor_price || ''}
              onChange={(e) => onChange({ distributor_price: parseFloat(e.target.value) || undefined })}
              className={`w-full pl-10 pr-4 py-3 rounded-lg border ${
                errors.distributor_price || validationErrors.length > 0
                  ? 'border-red-500 focus:border-red-500' 
                  : 'border-gray-600 focus:border-purple-500'
              } bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all`}
              placeholder="0.00"
              disabled={loading}
            />
          </div>
          {errors.distributor_price && (
            <p className="text-red-400 text-sm mt-1">{errors.distributor_price}</p>
          )}
          <p className="text-gray-400 text-xs mt-1">
            سعر الموزعين (اختياري)
          </p>
        </div>
      </div>

      {/* Inventory Management */}
      <div className="bg-gray-800/20 rounded-xl p-4 lg:p-6 border border-gray-700">
        <h3 className="text-lg font-medium text-white mb-6 flex items-center gap-2">
          <Package className="w-5 h-5" />
          إدارة المخزون
        </h3>

        {/* Digital Codes Section */}
        <div className="mb-8">
          <div className="bg-purple-900/20 rounded-lg p-4 border border-purple-500/30">
            <div className="flex items-center gap-2 mb-3">
              <Code className="w-5 h-5 text-purple-400" />
              <h4 className="text-sm font-medium text-white">منتجات بأكواد رقمية</h4>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-200">
                  أدخل الأكواد الرقمية
                </label>
                <textarea
                  value={digitalCodesInput}
                  onChange={(e) => handleDigitalCodesChange(e.target.value)}
                  placeholder="أدخل كل كود في سطر منفصل..."
                  rows={6}
                  className="w-full px-4 py-3 rounded-lg border border-gray-600 focus:border-purple-500 bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500/20 transition-all resize-none font-mono text-sm"
                  disabled={loading}
                />
                <p className="text-gray-400 text-xs mt-1">
                  أدخل كل كود رقمي في سطر منفصل. سيتم حساب الكمية تلقائياً.
                </p>
              </div>

              {digitalCodesInput.trim() && (
                <div className="bg-gray-700/30 rounded-lg p-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-300">الكمية المتاحة:</span>
                    <span className="text-purple-400 font-medium">
                      {calculateCodesQuantity(digitalCodesInput)} أكواد
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Non-Digital Products Section */}
        {!formData.has_digital_codes && (
          <div className="bg-blue-900/20 rounded-lg p-4 border border-blue-500/30">
            <div className="flex items-center gap-2 mb-4">
              <Hash className="w-5 h-5 text-blue-400" />
              <h4 className="text-sm font-medium text-white">منتجات بدون أكواد رقمية</h4>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Manual Quantity */}
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-200">
                  عدد الوحدات المتاحة
                </label>
                <input
                  type="number"
                  min="0"
                  value={formData.manual_quantity || ''}
                  onChange={(e) => handleManualQuantityChange(parseInt(e.target.value) || 0)}
                  className="w-full px-4 py-3 rounded-lg border border-gray-600 focus:border-blue-500 bg-gray-700/50 backdrop-blur-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 transition-all"
                  placeholder="0"
                  disabled={loading || formData.unlimited_stock}
                />
                <p className="text-gray-400 text-xs mt-1">
                  أدخل عدد الوحدات المتاحة للبيع
                </p>
              </div>

              {/* Unlimited Stock Checkbox */}
              <div className="flex flex-col justify-center">
                <label className="flex items-center gap-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.unlimited_stock || false}
                    onChange={(e) => handleUnlimitedStockChange(e.target.checked)}
                    className="w-5 h-5 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-500 focus:ring-2"
                    disabled={loading}
                  />
                  <div>
                    <span className="text-sm font-medium text-white">مخزون غير محدود</span>
                    <p className="text-xs text-gray-400">المنتج متاح دائماً للبيع</p>
                  </div>
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Inventory Status Preview */}
        <div className="bg-gray-700/30 rounded-lg p-4 mt-6">
          <h4 className="text-sm font-medium text-white mb-3 flex items-center gap-2">
            <Package className="w-4 h-4" />
            ملخص حالة المخزون
          </h4>
          <div className="space-y-2 text-sm">
            {formData.has_digital_codes ? (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">نوع المنتج:</span>
                  <span className="text-purple-400 font-medium">أكواد رقمية</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">الكمية المتاحة:</span>
                  <span className="text-white font-medium">
                    {calculateCodesQuantity(digitalCodesInput)} أكواد
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">حالة المخزون:</span>
                  <span className="text-green-400">✓ تتبع تلقائي</span>
                </div>
              </>
            ) : (
              <>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">نوع المنتج:</span>
                  <span className="text-blue-400 font-medium">منتج عادي</span>
                </div>
                {formData.unlimited_stock ? (
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">حالة المخزون:</span>
                    <span className="text-green-400">✓ مخزون غير محدود</span>
                  </div>
                ) : (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">الكمية المتاحة:</span>
                      <span className="text-white font-medium">{formData.manual_quantity || 0} وحدة</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">حالة المخزون:</span>
                      <span className={formData.manual_quantity && formData.manual_quantity > 0 ? 'text-green-400' : 'text-red-400'}>
                        {formData.manual_quantity && formData.manual_quantity > 0 ? '✓ متوفر' : '✗ غير متوفر'}
                      </span>
                    </div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <h4 className="text-red-400 font-medium mb-2">أخطاء في التسعير:</h4>
          <ul className="text-red-300 text-sm space-y-1">
            {validationErrors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Profit Calculations */}
      {calculations && validationErrors.length === 0 && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-6">
          <h4 className="text-green-400 font-medium mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            حسابات الربح
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* User Profit - IMPORTANT: This shows actual profit from customer purchases */}
            {/* When discount is active, customers buy at discount_price, so profit = discount_price - original_price */}
            {/* When no discount, customers buy at user_price, so profit = user_price - original_price */}
            <div className="bg-gray-800/50 rounded-lg p-4">
              <div className="text-gray-400 text-sm mb-1">ربح المستخدم</div>
              <div className="text-2xl font-bold text-green-400">
                ${calculations.userProfit.toFixed(2)}
              </div>
              <div className="text-xs text-gray-500">
                {formData.discount_price ? 'من السعر المخفض' : 'من السعر العادي'}
              </div>
            </div>

            {/* Distributor Profit - Independent of customer discounts */}
            {formData.distributor_price && (
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="text-gray-400 text-sm mb-1">ربح الموزع</div>
                <div className="text-2xl font-bold text-blue-400">
                  ${calculations.distributorProfit.toFixed(2)}
                </div>
                <div className="text-xs text-gray-500">
                  من سعر الموزع
                </div>
              </div>
            )}

            {/* Discount Percentage - Shows customer savings for marketing purposes */}
            {/* NOTE: This is the percentage discount customers see, not profit calculation */}
            {formData.discount_price && (
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="text-gray-400 text-sm mb-1">نسبة الخصم</div>
                <div className="text-2xl font-bold text-purple-400">
                  {calculations.discountPercentage.toFixed(1)}%
                </div>
                <div className="text-xs text-gray-500">
                  من السعر العادي
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Pricing Rules Info */}
      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <h4 className="text-blue-400 font-medium mb-2">قواعد التسعير:</h4>
        <ul className="text-blue-300 text-sm space-y-1">
          <li>• سعر المستخدم يجب أن يكون أكبر من السعر الأصلي</li>
          <li>• سعر الخصم يجب أن يكون أقل من سعر المستخدم وأكبر من السعر الأصلي</li>
          <li>• سعر الموزع يجب أن يكون أكبر من السعر الأصلي وأقل من سعر المستخدم</li>
          <li>• إذا تم تعيين سعر الخصم، سيتم حساب الربح بناءً عليه بدلاً من سعر المستخدم</li>
        </ul>
      </div>
    </div>
  )
}
