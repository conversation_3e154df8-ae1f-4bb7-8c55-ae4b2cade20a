import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../../lib/supabase/server'
import { rateLimit } from '../../../../lib/rate-limit'
import { 
  packageUpdateSchema, 
  validatePricingRules 
} from '../../../../lib/products'

// GET /api/admin/packages/[id] - Get single package
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return NextResponse.json({ error: 'ملف المستخدم غير موجود' }, { status: 404 })
    }

    // Get package with digital codes
    const { data: packageData, error } = await supabase
      .from('packages')
      .select(`
        *,
        digital_codes(
          id,
          key_encrypted,
          used,
          assigned_to_order_id,
          viewed_count,
          created_at
        )
      `)
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (error || !packageData) {
      return NextResponse.json({ 
        success: false,
        error: 'الحزمة غير موجودة' 
      }, { status: 404 })
    }

    // Convert digital codes back to text format for editing
    let digitalCodesText = ''
    if (packageData.digital_codes && packageData.digital_codes.length > 0) {
      digitalCodesText = packageData.digital_codes
        .map((code: any) => code.key_encrypted)
        .join('\n')
    }

    return NextResponse.json({ 
      success: true,
      data: {
        ...packageData,
        digital_codes: digitalCodesText
      }
    })

  } catch (error) {
    console.error('Error in package GET:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// PUT /api/admin/packages/[id] - Update package
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 10)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    const body = await request.json()
    
    // Validate input
    const validation = packageUpdateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({ 
        success: false,
        error: 'بيانات غير صالحة',
        details: validation.error.errors.map(e => e.message)
      }, { status: 400 })
    }

    const validatedData = validation.data

    // Validate pricing if provided
    if (validatedData.original_price && validatedData.user_price) {
      const pricingValidation = validatePricingRules({
        original_price: validatedData.original_price,
        user_price: validatedData.user_price,
        discount_price: validatedData.discount_price,
        distributor_price: validatedData.distributor_price
      })

      if (!pricingValidation.valid) {
        return NextResponse.json({
          success: false,
          error: 'أخطاء في التسعير',
          details: pricingValidation.errors
        }, { status: 400 })
      }
    }

    // Check if package exists and belongs to tenant
    const { data: existingPackage } = await supabase
      .from('packages')
      .select('id, product_id')
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!existingPackage) {
      return NextResponse.json({ 
        success: false,
        error: 'الحزمة غير موجودة' 
      }, { status: 404 })
    }

    // Process digital codes if provided
    let hasDigitalCodes = false
    let digitalCodesArray: string[] = []
    
    if (validatedData.digital_codes !== undefined) {
      if (validatedData.digital_codes && validatedData.digital_codes.trim()) {
        digitalCodesArray = validatedData.digital_codes
          .split('\n')
          .map(code => code.trim())
          .filter(code => code.length > 0)
        
        hasDigitalCodes = digitalCodesArray.length > 0
      }

      // Delete existing digital codes
      await supabase
        .from('digital_codes')
        .delete()
        .eq('package_id', id)
        .eq('tenant_id', profile.tenant_id)

      // Insert new digital codes if provided
      if (hasDigitalCodes && digitalCodesArray.length > 0) {
        const digitalCodeInserts = digitalCodesArray.map(code => ({
          tenant_id: profile.tenant_id,
          package_id: id,
          key_encrypted: code, // In production, this should be encrypted
          used: false,
          viewed_count: 0
        }))

        const { error: codesError } = await supabase
          .from('digital_codes')
          .insert(digitalCodeInserts)

        if (codesError) {
          console.error('Error updating digital codes:', codesError)
          return NextResponse.json({ 
            success: false,
            error: 'فشل في تحديث الرموز الرقمية' 
          }, { status: 500 })
        }
      }
    }

    // Prepare update data
    const updateData: any = {
      ...validatedData,
      updated_at: new Date().toISOString()
    }

    // Remove digital_codes from update data as it's handled separately
    delete updateData.digital_codes

    // Update inventory settings if digital codes changed
    if (validatedData.digital_codes !== undefined) {
      updateData.has_digital_codes = hasDigitalCodes
      updateData.track_inventory = !hasDigitalCodes
      updateData.unlimited_stock = !hasDigitalCodes
      updateData.manual_quantity = hasDigitalCodes ? 0 : 999999
    }

    // Update package
    const { data: packageData, error } = await supabase
      .from('packages')
      .update(updateData)
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating package:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في تحديث الحزمة' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      data: packageData 
    })

  } catch (error) {
    console.error('Error in package PUT:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// DELETE /api/admin/packages/[id] - Delete package
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'طلبات كثيرة جداً' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'غير مصرح' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'غير مصرح - مطلوب صلاحيات إدارية' }, { status: 403 })
    }

    // Check if package exists and belongs to tenant
    const { data: packageData } = await supabase
      .from('packages')
      .select('id')
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!packageData) {
      return NextResponse.json({ 
        success: false,
        error: 'الحزمة غير موجودة' 
      }, { status: 404 })
    }

    // Check if package has orders
    const { data: orders } = await supabase
      .from('orders')
      .select('id')
      .eq('package_id', id)
      .eq('tenant_id', profile.tenant_id)
      .limit(1)

    if (orders && orders.length > 0) {
      return NextResponse.json({ 
        success: false,
        error: 'لا يمكن حذف الحزمة لأنها تحتوي على طلبات' 
      }, { status: 400 })
    }

    // Delete digital codes first
    await supabase
      .from('digital_codes')
      .delete()
      .eq('package_id', id)
      .eq('tenant_id', profile.tenant_id)

    // Delete package
    const { error } = await supabase
      .from('packages')
      .delete()
      .eq('id', id)
      .eq('tenant_id', profile.tenant_id)

    if (error) {
      console.error('Error deleting package:', error)
      return NextResponse.json({ 
        success: false,
        error: 'فشل في حذف الحزمة' 
      }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true,
      message: 'تم حذف الحزمة بنجاح' 
    })

  } catch (error) {
    console.error('Error in package DELETE:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}
