import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { TenantResolver } from './app/lib/tenant'
import { addSecurityHeaders } from './app/lib/security-headers'
import { createServerClient } from '@supabase/ssr'

export async function middleware(request: NextRequest) {
  // Skip middleware for static files
  if (
    request.nextUrl.pathname.startsWith('/_next') ||
    request.nextUrl.pathname.includes('.') // Static files
  ) {
    // Still add security headers to all responses
    const response = NextResponse.next()
    return addSecurityHeaders(response)
  }

  // Handle API routes with authentication
  if (request.nextUrl.pathname.startsWith('/api/')) {
    return handleApiRoute(request)
  }

  try {
    // Resolve tenant from request
    const tenant = await TenantResolver.resolveTenantFromRequest(request)
    
    if (!tenant) {
      // If no tenant found and not in development, show tenant not found page
      if (process.env.NODE_ENV === 'production') {
        return NextResponse.redirect(new URL('/tenant-not-found', request.url))
      }
      
      // In development, try to get default tenant
      const defaultTenant = await TenantResolver.resolveTenantFromEnvironment()
      if (!defaultTenant) {
        console.warn('No tenant found and no default tenant available')
        return NextResponse.next()
      }
      
      // Set default tenant in headers for development
      const response = NextResponse.next()
      response.headers.set('x-tenant-id', defaultTenant.id)
      response.headers.set('x-tenant-slug', defaultTenant.slug)
      response.headers.set('x-tenant-name', encodeURIComponent(defaultTenant.name))
      response.headers.set('x-tenant-theme', encodeURIComponent(JSON.stringify(defaultTenant.theme_config)))
      return response
    }

    // Check if tenant is active
    if (tenant.status !== 'active') {
      if (tenant.status === 'suspended') {
        return NextResponse.redirect(new URL('/tenant-suspended', request.url))
      }
      if (tenant.status === 'inactive') {
        return NextResponse.redirect(new URL('/tenant-inactive', request.url))
      }
    }

    // Add tenant information to request headers (encode non-ASCII characters)
    const response = NextResponse.next()

    // Add security headers
    addSecurityHeaders(response)

    response.headers.set('x-tenant-id', tenant.id)
    response.headers.set('x-tenant-slug', tenant.slug)
    response.headers.set('x-tenant-name', encodeURIComponent(tenant.name))
    response.headers.set('x-tenant-theme', encodeURIComponent(JSON.stringify(tenant.theme_config)))
    response.headers.set('x-tenant-settings', encodeURIComponent(JSON.stringify(tenant.settings)))

    // Add tenant info to cookies with development-friendly settings
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge: 60 * 60 * 24 * 7, // 7 days for better development experience
      path: '/'
    }

    const clientCookieOptions = {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/'
    }

    response.cookies.set('tenant-id', tenant.id, cookieOptions)
    response.cookies.set('tenant-name', encodeURIComponent(tenant.name), clientCookieOptions)
    response.cookies.set('tenant-slug', tenant.slug, clientCookieOptions)

    // Add debug info for development
    if (process.env.NODE_ENV === 'development') {
      response.headers.set('x-debug-tenant', `${tenant.slug} (${tenant.id})`)
    }

    return response
  } catch (error) {
    console.error('Middleware error:', error)
    
    // In case of error, try to continue with default tenant
    try {
      const defaultTenant = await TenantResolver.getDefaultTenant()
      if (defaultTenant) {
        const response = NextResponse.next()
        response.headers.set('x-tenant-id', defaultTenant.id)
        response.headers.set('x-tenant-slug', defaultTenant.slug)
        response.headers.set('x-tenant-name', encodeURIComponent(defaultTenant.name))
        response.headers.set('x-tenant-theme', encodeURIComponent(JSON.stringify(defaultTenant.theme_config)))
        return response
      }
    } catch (fallbackError) {
      console.error('Fallback tenant resolution failed:', fallbackError)
    }
    
    return NextResponse.next()
  }
}

// Simple API route handler with authentication
async function handleApiRoute(request: NextRequest) {
  try {
    // Get tenant info first
    const tenant = await TenantResolver.resolveTenantFromRequest(request)
    const tenantId = tenant?.id || process.env.DEFAULT_TENANT_ID || 'default-tenant'

    // Create response with tenant headers
    const response = NextResponse.next()
    addSecurityHeaders(response)
    response.headers.set('x-tenant-id', tenantId)

    // For admin API routes, add user info to headers
    if (request.nextUrl.pathname.startsWith('/api/admin/')) {
      console.log('🔍 Middleware: Processing admin API route:', request.nextUrl.pathname)

      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            get(name: string) {
              return request.cookies.get(name)?.value
            },
            set() {}, // Not needed for middleware
            remove() {} // Not needed for middleware
          }
        }
      )

      const { data: { user } } = await supabase.auth.getUser()
      console.log('🔍 Middleware: User from session:', user ? `${user.email} (${user.id})` : 'null')

      if (user) {
        // Get user profile in one query
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('role, tenant_id, name')
          .eq('id', user.id)
          .single()

        if (profile) {
          // Add user info to headers for API routes to use
          response.headers.set('x-user-id', user.id)
          response.headers.set('x-user-email', user.email || '')
          response.headers.set('x-user-role', profile.role)
          response.headers.set('x-user-tenant', profile.tenant_id)
          response.headers.set('x-user-name', profile.name)
        }
      }
    }

    return response
  } catch (error) {
    console.error('API middleware error:', error)
    const response = NextResponse.next()
    addSecurityHeaders(response)
    return response
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}
