// Server Component for SSR - Better SEO and Performance
import Link from "next/link"
import { Tag } from "lucide-react"
import { createClient } from '@supabase/supabase-js'
import { TenantResolver } from "./lib/tenant"
// import CategorySection from "./components/CategorySection" // REMOVED: Product management system has been removed
import PromoBanner from "./components/PromoBanner"
// import PaginatedProductSection from "./components/PaginatedProductSection" // REMOVED: Product management system has been removed
import CategoryCard from "./components/CategoryCard"
import HomepageWrapper from "./components/HomepageWrapper"

// Secure server-side Supabase client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Server Component - Fetches data at build/request time
export default async function HomePage() {
  console.log('🔄 SSR: Loading homepage data...')

  // Get tenant slug (in production, extract from domain/subdomain)
  const tenantSlug = 'main'

  try {
    // 1. Resolve tenant securely
    const tenant = await TenantResolver.getTenantBySlug(tenantSlug)
    if (!tenant) {
      return (
        <div className="min-h-screen bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-white mb-4">Tenant not found</h1>
            <p className="text-gray-400">The requested tenant could not be found.</p>
          </div>
        </div>
      )
    }

    console.log('✅ SSR: Tenant resolved:', tenant.name)

    // 2. Fetch all homepage data in parallel (secure server-side)
    const [
      productsResult,
      categoriesResult,
      homepageSectionsResult
    ] = await Promise.allSettled([
      // Products - filtered by tenant (active = not deleted)
      supabaseAdmin
        .from('products')
        .select(`
          *,
          category:categories(id, name),
          packages(*)
        `)
        .eq('tenant_id', tenant.id)
        .is('deleted_at', null)
        .order('created_at', { ascending: false })
        .limit(12), // First 12 for homepage

      // Categories - filtered by tenant
      supabaseAdmin
        .from('categories')
        .select('*')
        .eq('tenant_id', tenant.id)
        .order('name')
        .limit(6), // Only first 6 for homepage

      // Homepage sections - filtered by tenant
      supabaseAdmin
        .from('homepage_sections')
        .select('*')
        .eq('tenant_id', tenant.id)
        .eq('is_active', true)
        .order('order_index')
    ])

    // Extract data safely
    const products = productsResult.status === 'fulfilled' ? productsResult.value.data || [] : []
    const categories = categoriesResult.status === 'fulfilled' ? categoriesResult.value.data || [] : []
    const homepageSections = homepageSectionsResult.status === 'fulfilled' ? homepageSectionsResult.value.data || [] : []

    console.log('📊 SSR: Data loaded successfully:', {
      products: products.length,
      categories: categories.length,
      homepageSections: homepageSections.length
    })

    // Calculate product count for each category
    const categoriesWithProductCount = categories.map(category => {
      const productCount = products.filter(product =>
        product.category_id === category.id
      ).length
      return { ...category, productCount }
    })

    // Get featured and latest products
    const featuredProducts = products.filter(product => product.featured).slice(0, 6)
    const latestProducts = products.slice(0, 8)

    // Get active homepage sections sorted by order
    const activeSections = homepageSections.filter((section) => section.is_active).sort((a, b) => (a.order_index || 0) - (b.order_index || 0))

    return (
      <HomepageWrapper
        initialProducts={products}
        initialCategories={categories}
        initialHomepageSections={homepageSections}
        tenant={tenant}
      >
        <div className="min-h-screen bg-gray-900">
          {/* Promotional Banner */}
          <section className="container mx-auto px-4 pt-4 pb-2">
            <PromoBanner />
          </section>

          {/* Categories Section */}
          {categoriesWithProductCount.length > 0 && (
            <section className="container mx-auto px-4 py-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
                    🏷️ تصفح حسب الفئة
                  </h2>
                  <p className="text-gray-400">
                    اكتشف منتجاتنا المنظمة حسب الفئات
                  </p>
                </div>
                <Link
                  href="/categories"
                  className="text-purple-400 hover:text-purple-300 transition-colors text-sm font-medium flex items-center space-x-1 space-x-reverse"
                >
                  <span>عرض الكل</span>
                  <Tag className="w-4 h-4" />
                </Link>
              </div>

              {/* Categories Grid */}
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 md:gap-4">
                {categoriesWithProductCount.map((category) => (
                  <CategoryCard
                    key={category.id}
                    category={category}
                    productCount={category.productCount}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Featured Products Section */}
          {featuredProducts.length > 0 && (
            <section className="container mx-auto px-4 py-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
                    ⭐ المنتجات المميزة
                  </h2>
                  <p className="text-gray-400">
                    أفضل منتجاتنا المختارة خصيصاً لك
                  </p>
                </div>
                <Link
                  href="/shop"
                  className="text-purple-400 hover:text-purple-300 transition-colors text-sm font-medium"
                >
                  عرض الكل
                </Link>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {featuredProducts.map((product) => (
                  <div key={product.id} className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors">
                    <Link href={`/product/${product.slug}`}>
                      <div className="aspect-square bg-gray-700 rounded-lg mb-3 overflow-hidden">
                        {product.cover_image && (
                          <img
                            src={product.cover_image}
                            alt={product.title}
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>
                      <h3 className="font-semibold text-white text-sm mb-1 line-clamp-2">
                        {product.title}
                      </h3>
                      <p className="text-purple-400 font-bold text-sm">
                        ${product.user_price || product.original_price || 'N/A'}
                      </p>
                    </Link>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Latest Products Section */}
          {latestProducts.length > 0 && (
            <section className="container mx-auto px-4 py-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl md:text-2xl font-bold text-white mb-2">
                    🆕 أحدث المنتجات
                  </h2>
                  <p className="text-gray-400">
                    آخر إضافاتنا من المنتجات الجديدة
                  </p>
                </div>
                <Link
                  href="/shop"
                  className="text-purple-400 hover:text-purple-300 transition-colors text-sm font-medium"
                >
                  عرض الكل
                </Link>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
                {latestProducts.map((product) => (
                  <div key={product.id} className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors">
                    <Link href={`/product/${product.slug}`}>
                      <div className="aspect-square bg-gray-700 rounded-lg mb-3 overflow-hidden">
                        {product.cover_image && (
                          <img
                            src={product.cover_image}
                            alt={product.title}
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>
                      <h3 className="font-semibold text-white text-sm mb-1 line-clamp-2">
                        {product.title}
                      </h3>
                      <p className="text-purple-400 font-bold text-sm">
                        ${product.user_price || product.original_price || 'N/A'}
                      </p>
                    </Link>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Dynamic Homepage Sections */}
          {activeSections.map((section) => (
            <section key={section.id} className="container mx-auto px-4 py-6">
              <div className="bg-gray-800 rounded-lg p-6">
                <h2 className="text-2xl font-bold text-white mb-4">{section.title}</h2>
                <div
                  className="text-gray-300"
                  dangerouslySetInnerHTML={{ __html: section.content || '' }}
                />
              </div>
            </section>
          ))}

          {/* All Products Link */}
          {products.length > 0 && (
            <section className="container mx-auto px-4 py-8">
              <div className="text-center">
                <Link
                  href="/shop"
                  className="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-colors"
                >
                  عرض جميع المنتجات
                </Link>
              </div>
            </section>
          )}
        </div>
      </HomepageWrapper>
    )

  } catch (error) {
    console.error('❌ SSR: Error loading homepage:', error)

    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">خطأ في تحميل الصفحة</h1>
          <p className="text-gray-400">حدث خطأ أثناء تحميل بيانات الصفحة الرئيسية</p>
        </div>
      </div>
    )
  }
}
