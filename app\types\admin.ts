import { NextResponse } from 'next/server'

// Base types for admin operations
export type UserRole = 'admin' | 'distributor' | 'user' | 'worker'
export type OrderStatus = 'pending' | 'processing' | 'completed' | 'cancelled' | 'refunded'
export type BalanceOperation = 'add' | 'subtract' | 'set'

// User Profile interface for admin operations
export interface AdminUserProfile {
  id: string
  tenant_id: string
  name: string
  email: string
  role: UserRole
  phone?: string
  avatar?: string
  preferred_currency?: string
  wallet_balance?: number
  is_active: boolean
  created_at: string
  updated_at: string
  banned?: boolean
  ban_reason?: string
}

// Tenant interface
export interface Tenant {
  id: string
  name: string
  status: 'active' | 'inactive' | 'suspended'
  is_active: boolean
  created_at: string
  updated_at: string
}

// Product interfaces
export interface AdminProduct {
  id: string
  tenant_id: string
  title: string
  slug: string
  description: string
  category_id: string
  cover_image?: string
  tags?: string[]
  featured: boolean

  // Pricing fields (only used when no packages exist)
  original_price?: number
  user_price?: number
  discount_price?: number
  distributor_price?: number

  created_at: string
  updated_at: string

  // Virtual fields
  packages_count?: number
  category_name?: string
}

export interface AdminPackage {
  id: string
  product_id: string
  tenant_id: string
  name: string
  description?: string
  original_price: number
  user_price: number
  discount_price?: number
  distributor_price?: number
  image?: string
  created_at: string
  updated_at: string
}

export interface AdminCustomField {
  id: string
  tenant_id: string
  product_id: string
  label: string
  field_type: 'text' | 'dropdown'
  description?: string
  required: boolean
  placeholder?: string
  field_order: number
  options?: Array<{
    label: string
    value: string
  }>
  created_at: string
  updated_at: string
}

export interface AdminCategory {
  id: string
  name: string
  slug: string
  tenant_id: string
  created_at: string
  updated_at: string
}

// Currency interfaces
export interface AdminCurrency {
  id: string
  tenant_id: string
  code: string
  name: string
  symbol: string
  exchange_rate: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// Balance operation interfaces
export interface UserCurrencyBalance {
  id: string
  user_id: string
  currency_code: string
  balance: number
  created_at: string
  updated_at: string
}

export interface BalanceOperationRequest {
  currency_code: string
  amount: number
  operation: BalanceOperation
  notes?: string
}

// Order interfaces for admin operations
export interface AdminOrder {
  id: string
  tenant_id: string
  user_id: string
  product_id: string
  package_id?: string
  amount: number
  currency_code: string
  status: OrderStatus
  custom_fields?: Record<string, any>
  worker_id?: string
  worker_action?: 'accepted' | 'rejected'
  worker_action_at?: string
  created_at: string
  updated_at: string
  products?: {
    id: string
    title: string
    slug: string
    cover_image: string
  }
  packages?: {
    id: string
    name: string
    original_price: number
    user_price: number
    image: string
  }
  user_profiles?: {
    id: string
    name: string
    email: string
  }
  worker_profiles?: {
    id: string
    name: string
    email: string
    role: string
  }
}

// Earnings interfaces
export interface EarningsData {
  totalRevenue: number
  totalCost: number
  totalProfit: number
  orderCount: number
  averageProfitPerOrder: number
}

export interface EarningsByPeriod extends EarningsData {
  period: number
}

export interface TopProfitableProduct {
  id: string
  title: string
  totalProfit: number
  orderCount: number
  averageProfit: number
}

export interface EarningsByCurrency {
  [currencyCode: string]: EarningsData
}

// API Response interfaces
export interface AdminApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  details?: string
}

export interface PaginatedResponse<T> extends AdminApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Supabase client type (to replace 'any')
export interface SupabaseClient {
  auth: {
    getUser(): Promise<{ data: { user: any }, error: any }>
  }
  from(table: string): any
}

// Audit log interface
export interface AuditLogDetails {
  ip?: string
  productId?: string
  productTitle?: string
  hasData?: boolean
  error?: string
  stack?: string
  userId?: string
  tenantId?: string
  resultCount?: number
  totalCount?: number
  page?: number
  params?: Record<string, any>
  [key: string]: any
}

// Rate limiting interfaces
export interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
}

export interface RateLimitConfig {
  maxRequests: number
  windowMs: number
}

// Function type definitions for admin handlers
export type AdminHandler = (
  supabase: SupabaseClient,
  profile: AdminUserProfile,
  data: any,
  ip: string
) => Promise<NextResponse>

export type AdminHandlerWithId = (
  supabase: SupabaseClient,
  profile: AdminUserProfile,
  data: any,
  id: string,
  ip: string
) => Promise<NextResponse>

export type AdminGetHandler = (
  supabase: SupabaseClient,
  profile: AdminUserProfile,
  id: string,
  ip: string
) => Promise<NextResponse>

export type AdminDeleteHandler = (
  supabase: SupabaseClient,
  profile: AdminUserProfile,
  id: string,
  ip: string
) => Promise<NextResponse>

// Validation result interface
export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

// Product creation/update data interfaces removed - product management system has been removed

// User creation/update data interfaces
export interface UserCreateData {
  email: string
  name: string
  role: UserRole
  phone?: string
  password: string
}

export interface UserUpdateData {
  name?: string
  role?: UserRole
  phone?: string
  preferred_currency?: string
  is_active?: boolean
}

// Currency creation/update data interfaces
export interface CurrencyCreateData {
  code: string
  name: string
  symbol: string
  exchange_rate: number
  is_active?: boolean
}

export interface CurrencyUpdateData {
  name?: string
  exchange_rate?: number
  is_active?: boolean
}
