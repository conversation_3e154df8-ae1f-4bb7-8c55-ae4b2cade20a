import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'
import { z } from 'zod'
import { rateLimit } from '../../../lib/rate-limit'
import {
  AdminCurrency,
  CurrencyCreateData,
  CurrencyUpdateData,
  AdminUserProfile
} from '../../../types/admin'

// Validation schemas
const currencySchema = z.object({
  code: z.string().regex(/^[A-Z]{3}$/, "Currency code must be 3 uppercase letters"),
  name: z.string().min(1).max(50),
  symbol: z.string().min(1).max(10),
  exchange_rate: z.number().positive().max(999999),
  is_active: z.boolean().optional()
})

const updateRateSchema = z.object({
  code: z.string().regex(/^[A-Z]{3}$/),
  exchange_rate: z.number().positive().max(999999)
})

const updateCurrencySchema = z.object({
  code: z.string().regex(/^[A-Z]{3}$/),
  name: z.string().min(1).max(50).optional(),
  exchange_rate: z.number().positive().max(999999).optional(),
  is_active: z.boolean().optional()
})

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()

    // Get tenant information from headers or use default
    let tenantId = request.headers.get('x-tenant-id')
    if (!tenantId) {
      tenantId = process.env.DEFAULT_TENANT_ID || 'default-tenant'
      console.log('No tenant ID in headers, using default:', tenantId)
    }

    // Get currencies for this tenant
    const { data: currencies, error } = await supabase
      .from('currencies')
      .select('*')
      .eq('tenant_id', tenantId)
      .order('code')

    if (error) throw error

    return NextResponse.json({ currencies })
  } catch (error) {
    console.error('Error fetching currencies:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = await createClient()

    // Get tenant information from headers or use default
    let tenantId = request.headers.get('x-tenant-id')
    if (!tenantId) {
      tenantId = process.env.DEFAULT_TENANT_ID || 'default-tenant'
      console.log('No tenant ID in headers, using default:', tenantId)
    }

    const body = await request.json()
    const validatedData: CurrencyCreateData = currencySchema.parse(body)

    // Prevent creating USD (it's hardcoded)
    if (validatedData.code === 'USD') {
      return NextResponse.json({ error: 'USD is the default currency and cannot be modified' }, { status: 400 })
    }

    // Insert new currency
    const { data: currency, error } = await supabase
      .from('currencies')
      .insert({
        ...validatedData,
        tenant_id: tenantId
      })
      .select()
      .single()

    if (error) {
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json({ error: 'Currency already exists' }, { status: 409 })
      }
      throw error
    }

    return NextResponse.json({ currency }, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 })
    }
    console.error('Error creating currency:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest): Promise<NextResponse> {
  try {
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { code, name, exchange_rate, is_active } = updateCurrencySchema.parse(body)

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Prevent modifying USD (except activation status)
    if (code === 'USD' && (name !== undefined || exchange_rate !== undefined)) {
      return NextResponse.json({ error: 'USD name and exchange rate cannot be modified' }, { status: 400 })
    }

    // Build update object with only provided fields
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (name !== undefined) updateData.name = name
    if (exchange_rate !== undefined) updateData.exchange_rate = exchange_rate
    if (is_active !== undefined) updateData.is_active = is_active

    // Update currency
    const { data: currency, error } = await supabase
      .from('currencies')
      .update(updateData)
      .eq('code', code)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (error) throw error

    if (!currency) {
      return NextResponse.json({ error: 'Currency not found' }, { status: 404 })
    }

    return NextResponse.json({ currency })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 })
    }
    console.error('Error updating currency:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest): Promise<NextResponse> {
  try {
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { code, is_active } = body

    if (!code || typeof is_active !== 'boolean') {
      return NextResponse.json({ error: 'Invalid request body' }, { status: 400 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Prevent disabling USD
    if (code === 'USD' && !is_active) {
      return NextResponse.json({ error: 'USD cannot be disabled' }, { status: 400 })
    }

    // Update currency status
    const { data: currency, error } = await supabase
      .from('currencies')
      .update({ 
        is_active,
        updated_at: new Date().toISOString()
      })
      .eq('code', code)
      .eq('tenant_id', profile.tenant_id)
      .select()
      .single()

    if (error) throw error

    if (!currency) {
      return NextResponse.json({ error: 'Currency not found' }, { status: 404 })
    }

    return NextResponse.json({ currency })
  } catch (error) {
    console.error('Error updating currency status:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest): Promise<NextResponse> {
  try {
    // Rate limiting
    const identifier = request.headers.get('x-forwarded-for') || 'anonymous'
    if (!rateLimit(identifier, 5)) {
      return NextResponse.json({ error: 'Too many requests' }, { status: 429 })
    }

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's tenant and verify admin role
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('role, tenant_id')
      .eq('id', user.id)
      .single()

    if (!profile || profile.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { code } = await request.json()

    // Validate input
    if (!code || typeof code !== 'string') {
      return NextResponse.json({ error: 'Currency code is required' }, { status: 400 })
    }

    // Prevent deletion of USD
    if (code === 'USD') {
      return NextResponse.json({ error: 'Cannot delete USD currency' }, { status: 400 })
    }

    // Check if currency exists and belongs to this tenant
    const { data: existingCurrency } = await supabase
      .from('currencies')
      .select('code')
      .eq('code', code)
      .eq('tenant_id', profile.tenant_id)
      .single()

    if (!existingCurrency) {
      return NextResponse.json({ error: 'Currency not found' }, { status: 404 })
    }

    // Check if currency is being used in user balances
    const { data: balances } = await supabase
      .from('user_currency_balances')
      .select('id')
      .eq('currency_code', code)
      .limit(1)

    if (balances && balances.length > 0) {
      return NextResponse.json({
        error: 'Cannot delete currency that has user balances. Please contact users to withdraw their funds first.'
      }, { status: 400 })
    }

    // Delete the currency
    const { error } = await supabase
      .from('currencies')
      .delete()
      .eq('code', code)
      .eq('tenant_id', profile.tenant_id)

    if (error) throw error

    return NextResponse.json({ message: 'Currency deleted successfully' })
  } catch (error) {
    console.error('Error deleting currency:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
