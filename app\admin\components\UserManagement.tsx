"use client"

import { useState, useEffect } from "react"
import { Eye, Edit, Ban, UserCheck, X, Save, Wallet, RefreshCw } from "lucide-react"
import type { User } from "../../types"
import { toast } from "sonner"
import BalanceManagement from "./BalanceManagement"

interface UserWithBalances extends User {
  currencyBalances: Array<{
    currencyCode: string
    balance: number
    currencyName: string
    exchangeRate: number
  }>
}

export default function UserManagement() {
  // Simple state management since DataContext was simplified
  const [orders, setOrders] = useState<any[]>([])
  const [currencies, setCurrencies] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [users, setUsers] = useState<UserWithBalances[]>([])
  const [selectedUser, setSelectedUser] = useState<UserWithBalances | null>(null)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editForm, setEditForm] = useState<Partial<User>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingUsers, setIsLoadingUsers] = useState(true)
  const [balanceManagementUser, setBalanceManagementUser] = useState<{ id: string; name: string } | null>(null)

  // Load users from API
  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      setIsLoadingUsers(true)
      // Simplified fetch without cache-busting
      const response = await fetch('/api/admin/users')

      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }

      const data = await response.json()
      console.log('UserManagement - Fetched users:', data.users?.length || 0)
      setUsers(data.users || [])
    } catch (error) {
      console.error('Error fetching users:', error)
      toast.error('فشل في تحميل المستخدمين')
    } finally {
      setIsLoadingUsers(false)
    }
  }

  const openViewModal = (user: UserWithBalances) => {
    setSelectedUser(user)
    setIsViewModalOpen(true)
  }

  const openEditModal = (user: UserWithBalances) => {
    setSelectedUser(user)
    setEditForm(user)
    setIsEditModalOpen(true)
  }

  const closeModals = () => {
    setIsViewModalOpen(false)
    setIsEditModalOpen(false)
    setSelectedUser(null)
    setEditForm({})
  }

  const handleSaveUser = async () => {
    if (!selectedUser) return

    try {
      setIsLoading(true)

      const response = await fetch(`/api/admin/users/${selectedUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editForm.name,
          role: editForm.role,
          phone: editForm.phone
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update user')
      }

      toast.success('تم تحديث المستخدم بنجاح')
      await fetchUsers() // Refresh users list
      closeModals()
    } catch (error) {
      console.error('Error updating user:', error)
      toast.error('فشل في تحديث المستخدم')
    } finally {
      setIsLoading(false)
    }
  }

  const handleBanUser = async (userId: string) => {
    if (!confirm("هل أنت متأكد من حظر هذا المستخدم؟")) return

    try {
      setIsLoading(true)

      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to ban user')
      }

      toast.success('تم حظر المستخدم بنجاح')
      await fetchUsers() // Refresh users list
    } catch (error) {
      console.error('Error banning user:', error)
      toast.error('فشل في حظر المستخدم')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePromoteUser = async (userId: string, newRole: "admin" | "distributor" | "user") => {
    if (!confirm(`هل أنت متأكد من ترقية هذا المستخدم إلى ${newRole}؟`)) return

    setIsLoading(true)
    // TODO: Implement role change with Supabase
    await new Promise((resolve) => setTimeout(resolve, 500))

    setUsers((prev) => prev.map((user) => (user.id === userId ? { ...user, role: newRole } : user)))

    setIsLoading(false)
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h2 className="text-xl md:text-2xl font-bold">إدارة المستخدمين</h2>
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-400">{users.length} مستخدم إجمالي</div>
          <button
            onClick={fetchUsers}
            disabled={isLoadingUsers}
            className="flex items-center gap-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white text-sm rounded-lg transition-colors"
            title="تحديث قائمة المستخدمين"
          >
            <RefreshCw className={`w-4 h-4 ${isLoadingUsers ? 'animate-spin' : ''}`} />
            <span>تحديث</span>
          </button>
        </div>
      </div>

      <div className="bg-gray-800/50 backdrop-blur-md rounded-xl border border-gray-700/50 shadow-xl">
        <div className="p-4 md:p-6">
          {isLoadingUsers ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
              <p className="text-gray-400">جاري تحميل المستخدمين...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-400">لا يوجد مستخدمون في هذا المتجر</p>
            </div>
          ) : (
            <>
              {/* Desktop Table */}
              <div className="hidden lg:block overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-right text-gray-400 text-sm">
                  <th className="pb-3">المستخدم</th>
                  <th className="pb-3">الدور</th>
                  <th className="pb-3">الأرصدة</th>
                  <th className="pb-3">الطلبات</th>
                  <th className="pb-3">الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => {
                  const userOrders = orders.filter((o) => o.user_id === user.id)
                  return (
                    <tr key={user.id} className="border-t border-gray-700/50">
                      <td className="py-4">
                        <div>
                          <p className="font-semibold">{user.name}</p>
                          <p className="text-sm text-gray-400">{user.email}</p>
                        </div>
                      </td>
                      <td className="py-4">
                        <span
                          className={`px-2 py-1 rounded-xl text-xs ${
                            user.role === "admin"
                              ? "bg-red-400/10 text-red-400"
                              : user.role === "distributor"
                                ? "bg-blue-400/10 text-blue-400"
                                : user.role === "worker"
                                  ? "bg-green-400/10 text-green-400"
                                  : "bg-gray-400/10 text-gray-400"
                          }`}
                        >
                          {user.role === "admin" ? "مدير" : user.role === "distributor" ? "موزع" : user.role === "worker" ? "عامل" : "مستخدم"}
                        </span>
                      </td>
                      <td className="py-4">
                        {user.currencyBalances.length > 0 ? (
                          <div className="space-y-1">
                            {user.currencyBalances.slice(0, 2).map((balance) => (
                              <div key={balance.currencyCode} className="text-sm">
                                <span className="font-semibold">{balance.balance.toFixed(2)}</span>
                                <span className="text-gray-400 ml-1">{balance.currencyCode}</span>
                              </div>
                            ))}
                            {user.currencyBalances.length > 2 && (
                              <div className="text-xs text-gray-500">
                                +{user.currencyBalances.length - 2} عملة أخرى
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-500 text-sm">لا توجد أرصدة</span>
                        )}
                      </td>
                      <td className="py-4">{userOrders.length}</td>
                      <td className="py-4">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => openViewModal(user)}
                            className="p-2 text-gray-400 hover:text-blue-400 transition-colors rounded-xl hover:bg-blue-400/10"
                            title="عرض التفاصيل"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => openEditModal(user)}
                            className="p-2 text-gray-400 hover:text-purple-400 transition-colors rounded-xl hover:bg-purple-400/10"
                            title="تعديل"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleBanUser(user.id)}
                            className="p-2 text-gray-400 hover:text-red-400 transition-colors rounded-xl hover:bg-red-400/10"
                            title="حظر"
                            disabled={isLoading}
                          >
                            <Ban className="w-4 h-4" />
                          </button>
                          {user.role !== "admin" && (
                            <button
                              onClick={() => handlePromoteUser(user.id, user.role === "user" ? "distributor" : "admin")}
                              className="p-2 text-gray-400 hover:text-green-400 transition-colors rounded-xl hover:bg-green-400/10"
                              title="ترقية"
                              disabled={isLoading}
                            >
                              <UserCheck className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="lg:hidden grid grid-cols-1 sm:grid-cols-2 gap-4">
            {users.map((user) => {
              const userOrders = orders.filter((o) => o.user_id === user.id)
              return (
                <div key={user.id} className="bg-gray-700/30 backdrop-blur-sm rounded-xl p-4 border border-gray-600/50">
                  {/* User Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-lg truncate">{user.name}</h3>
                      <p className="text-sm text-gray-400 truncate">{user.email}</p>
                      <div className="mt-2">
                        <span
                          className={`px-3 py-1 rounded-xl text-xs font-medium ${
                            user.role === "admin"
                              ? "bg-red-400/10 text-red-400"
                              : user.role === "distributor"
                                ? "bg-blue-400/10 text-blue-400"
                                : user.role === "worker"
                                  ? "bg-green-400/10 text-green-400"
                                  : "bg-gray-400/10 text-gray-400"
                          }`}
                        >
                          {user.role === "admin" ? "مدير" : user.role === "distributor" ? "موزع" : user.role === "worker" ? "عامل" : "مستخدم"}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* User Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-gray-400 mb-1">الأرصدة</p>
                      {user.currencyBalances.length > 0 ? (
                        <div className="space-y-1">
                          {user.currencyBalances.slice(0, 2).map((balance) => (
                            <div key={balance.currencyCode} className="text-sm font-bold text-green-400">
                              {balance.balance.toFixed(2)} {balance.currencyCode}
                            </div>
                          ))}
                          {user.currencyBalances.length > 2 && (
                            <div className="text-xs text-gray-500">+{user.currencyBalances.length - 2} أخرى</div>
                          )}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">لا توجد أرصدة</p>
                      )}
                    </div>
                    <div>
                      <p className="text-xs text-gray-400 mb-1">عدد الطلبات</p>
                      <p className="text-lg font-bold text-blue-400">{userOrders.length}</p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="grid grid-cols-2 gap-2 pt-3 border-t border-gray-600/50">
                    <button
                      onClick={() => openViewModal(user)}
                      className="flex items-center justify-center space-x-2 space-x-reverse px-3 py-2 bg-blue-600/20 text-blue-400 hover:bg-blue-600/30 transition-colors rounded-xl"
                    >
                      <Eye className="w-4 h-4" />
                      <span className="text-sm font-medium">عرض</span>
                    </button>
                    <button
                      onClick={() => openEditModal(user)}
                      className="flex items-center justify-center space-x-2 space-x-reverse px-3 py-2 bg-purple-600/20 text-purple-400 hover:bg-purple-600/30 transition-colors rounded-xl"
                    >
                      <Edit className="w-4 h-4" />
                      <span className="text-sm font-medium">تعديل</span>
                    </button>
                    <button
                      onClick={() => handleBanUser(user.id)}
                      className="flex items-center justify-center space-x-2 space-x-reverse px-3 py-2 bg-red-600/20 text-red-400 hover:bg-red-600/30 transition-colors rounded-xl"
                      disabled={isLoading}
                    >
                      <Ban className="w-4 h-4" />
                      <span className="text-sm font-medium">حظر</span>
                    </button>
                    {user.role !== "admin" && (
                      <button
                        onClick={() => handlePromoteUser(user.id, user.role === "user" ? "distributor" : "admin")}
                        className="flex items-center justify-center space-x-2 space-x-reverse px-3 py-2 bg-green-600/20 text-green-400 hover:bg-green-600/30 transition-colors rounded-xl"
                        disabled={isLoading}
                      >
                        <UserCheck className="w-4 h-4" />
                        <span className="text-sm font-medium">ترقية</span>
                      </button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
            </>
          )}
        </div>
      </div>

      {/* View User Modal */}
      {isViewModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 md:p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-2xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl">
            <div className="p-4 md:p-6 border-b border-gray-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-lg md:text-2xl font-bold">تفاصيل المستخدم</h3>
                <button onClick={closeModals} className="text-gray-400 hover:text-white p-2 rounded-xl hover:bg-gray-700/50 transition-colors">
                  <X className="w-5 h-5 md:w-6 md:h-6" />
                </button>
              </div>
            </div>

            <div className="p-4 md:p-6 space-y-4 md:space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                  <h4 className="font-semibold mb-2">الاسم</h4>
                  <p className="text-gray-300">{selectedUser.name}</p>
                </div>
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                  <h4 className="font-semibold mb-2">البريد الإلكتروني</h4>
                  <p className="text-gray-300">{selectedUser.email}</p>
                </div>
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                  <h4 className="font-semibold mb-2">الدور</h4>
                  <p className="text-gray-300">
                    {selectedUser.role === "admin" ? "مدير" : selectedUser.role === "distributor" ? "موزع" : selectedUser.role === "worker" ? "عامل" : "مستخدم"}
                  </p>
                </div>
                <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                  <h4 className="font-semibold mb-2">الأرصدة</h4>
                  {selectedUser.currencyBalances.length > 0 ? (
                    <div className="space-y-2">
                      {selectedUser.currencyBalances.map((balance) => (
                        <div key={balance.currencyCode} className="flex justify-between items-center">
                          <span className="text-gray-400">{balance.currencyName}</span>
                          <span className="text-green-400 font-bold">
                            {balance.balance.toFixed(2)} {balance.currencyCode}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">لا توجد أرصدة</p>
                  )}
                </div>
              </div>

              <div className="bg-gray-700/50 backdrop-blur-sm rounded-lg p-4 border border-gray-600/50">
                <h4 className="font-semibold mb-2">إحصائيات الطلبات</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-400">
                      {orders.filter((o) => o.user_id === selectedUser.id).length}
                    </p>
                    <p className="text-sm text-gray-400">إجمالي الطلبات</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-400">
                      {orders.filter((o) => o.user_id === selectedUser.id && o.status === "completed").length}
                    </p>
                    <p className="text-sm text-gray-400">مكتملة</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-yellow-400">
                      $
                      {orders
                        .filter((o) => o.user_id === selectedUser.id)
                        .reduce((sum, o) => sum + o.amount, 0)
                        .toFixed(2)}
                    </p>
                    <p className="text-sm text-gray-400">إجمالي الإنفاق</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {isEditModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 md:p-4">
          <div className="bg-gray-800/90 backdrop-blur-md rounded-xl max-w-md w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/50 shadow-2xl">
            <div className="p-4 md:p-6 border-b border-gray-700/50">
              <div className="flex items-center justify-between">
                <h3 className="text-lg md:text-2xl font-bold">تعديل المستخدم</h3>
                <button onClick={closeModals} className="text-gray-400 hover:text-white p-2 rounded-xl hover:bg-gray-700/50 transition-colors">
                  <X className="w-5 h-5 md:w-6 md:h-6" />
                </button>
              </div>
            </div>

            <div className="p-4 md:p-6 space-y-4 md:space-y-6">
              {/* Read-only Name Field */}
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-400">الاسم (للعرض فقط)</label>
                <div className="w-full bg-gray-800/50 border border-gray-600/30 rounded-xl px-3 py-2 text-gray-300 cursor-not-allowed">
                  {selectedUser.name}
                </div>
                <p className="text-xs text-gray-500 mt-1">لا يمكن تعديل الاسم من هنا</p>
              </div>

              {/* Read-only Email Field */}
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-400">البريد الإلكتروني (للعرض فقط)</label>
                <div className="w-full bg-gray-800/50 border border-gray-600/30 rounded-xl px-3 py-2 text-gray-300 cursor-not-allowed">
                  {selectedUser.email}
                </div>
                <p className="text-xs text-gray-500 mt-1">لا يمكن تعديل البريد الإلكتروني من هنا</p>
              </div>



              {/* Balance Management Link */}
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Wallet className="w-5 h-5 text-blue-400" />
                  <div>
                    <h4 className="font-medium text-blue-400">إدارة الأرصدة</h4>
                    <p className="text-sm text-gray-400">لإدارة أرصدة العملات المختلفة للمستخدم</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => {
                    if (selectedUser) {
                      setBalanceManagementUser({ id: selectedUser.id, name: selectedUser.name })
                    }
                  }}
                  className="mt-3 w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  إدارة الأرصدة
                </button>
              </div>

              {/* Editable Role */}
              <div>
                <label className="block text-sm font-medium mb-2">الدور</label>
                <select
                  value={editForm.role || "user"}
                  onChange={(e) =>
                    setEditForm((prev) => ({ ...prev, role: e.target.value as "admin" | "distributor" | "user" | "worker" }))
                  }
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300"
                >
                  <option value="user">مستخدم</option>
                  <option value="distributor">موزع</option>
                  <option value="worker">عامل</option>
                  <option value="admin">مدير</option>
                </select>
              </div>

              <div className="flex space-x-4 space-x-reverse pt-4">
                <button
                  onClick={handleSaveUser}
                  disabled={isLoading}
                  className="flex-1 btn-primary flex items-center justify-center space-x-2 space-x-reverse"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <>
                      <Save className="w-5 h-5" />
                      <span>حفظ التغييرات</span>
                    </>
                  )}
                </button>
                <button onClick={closeModals} disabled={isLoading} className="flex-1 btn-secondary">
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Balance Management Modal */}
      {balanceManagementUser && (
        <BalanceManagement
          userId={balanceManagementUser.id}
          userName={balanceManagementUser.name}
          isOpen={!!balanceManagementUser}
          onClose={() => setBalanceManagementUser(null)}
        />
      )}
    </div>
  )
}
