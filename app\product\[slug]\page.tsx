"use client"

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { ArrowLeft, Star, ShoppingCart, Package, Check, AlertCircle, Plus, Minus } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import type { Product, Package as ProductPackage } from '../../types'
import PurchaseFlow from '../../components/PurchaseFlow'

interface ProductWithDetails extends Omit<Product, 'custom_fields'> {
  category?: {
    id: string
    name: string
    slug: string
  }
  packages: ProductPackage[]
  custom_fields: {
    id: string
    label: string
    field_type: 'text' | 'dropdown' | 'email' | 'number'
    required: boolean
    placeholder?: string
    description?: string
    field_order: number
    validation_rules?: any
    display_options?: any
    options?: Array<{ value: string; label: string }>
  }[]
}

export default function ProductDetailPage() {
  const params = useParams()
  const slug = params.slug as string
  
  const [product, setProduct] = useState<ProductWithDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPackage, setSelectedPackage] = useState<ProductPackage | null>(null)

  // Fetch product data
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const response = await fetch(`/api/products/${slug}`)
        const data = await response.json()

        if (data.success) {
          setProduct(data.data)
          // Auto-select first package if available
          if (data.data.packages && data.data.packages.length > 0) {
            setSelectedPackage(data.data.packages[0])
          }
        } else {
          setError(data.error || 'فشل في تحميل المنتج')
        }
      } catch (err) {
        setError('حدث خطأ في تحميل المنتج')
      } finally {
        setLoading(false)
      }
    }

    if (slug) {
      fetchProduct()
    }
  }, [slug])

  // Get effective price
  const getEffectivePrice = (item: ProductWithDetails | ProductPackage) => {
    if (item.discount_price) return item.discount_price
    return item.user_price || 0
  }

  // Check if has discount
  const hasDiscount = (item: ProductWithDetails | ProductPackage) => {
    return item.discount_price && item.user_price && item.discount_price < item.user_price
  }

  // Calculate discount percentage
  const getDiscountPercentage = (item: ProductWithDetails | ProductPackage) => {
    if (!hasDiscount(item)) return 0
    const original = item.user_price!
    const discounted = item.discount_price!
    return Math.round(((original - discounted) / original) * 100)
  }

  // Handle purchase
  const handlePurchase = (data: {
    product: string
    package?: string
    customFields: Record<string, string>
    quantity: number
  }) => {
    // TODO: Implement purchase logic
    console.log('Purchase:', data)
    alert('تم إضافة المنتج إلى السلة!')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-300 mb-2">خطأ في تحميل المنتج</h3>
            <p className="text-gray-500 mb-4">{error}</p>
            <Link href="/shop" className="text-purple-400 hover:text-purple-300">
              العودة إلى المتجر
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Back Button */}
        <Link href="/shop" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-6">
          <ArrowLeft className="w-5 h-5 mr-2" />
          العودة إلى المتجر
        </Link>

        {/* PWOP Layout - Product image first and big, then name, description, price */}
        {(!product.packages || product.packages.length === 0) ? (
          <div className="max-w-2xl mx-auto pb-32">
            {/* Large Product Image */}
            <div className="relative aspect-video rounded-2xl overflow-hidden bg-gray-800 mb-6">
              {product.cover_image ? (
                <Image
                  src={product.cover_image}
                  alt={product.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 50vw"
                  priority
                  onError={(e) => {
                    const target = e.target as HTMLImageElement
                    target.src = "/logo.jpg"
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Package className="w-16 h-16 text-gray-400" />
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="text-center mb-8">
              <div className="text-sm text-purple-400 mb-2">{product.category?.name}</div>
              <h1 className="text-3xl font-bold text-white mb-4">{product.title}</h1>
              <p className="text-gray-300 leading-relaxed mb-6">{product.description}</p>

              {/* Price Display */}
              <div className="bg-gray-800/50 rounded-lg p-6 mb-6">
                <div className="text-center">
                  {hasDiscount(product) && (
                    <div className="inline-block bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-bold mb-3">
                      {getDiscountPercentage(product)}% خصم
                    </div>
                  )}
                  <div className="text-4xl font-bold text-purple-400 mb-2">
                    ${getEffectivePrice(product).toFixed(2)}
                  </div>
                  {hasDiscount(product) && (
                    <div className="text-lg text-gray-500 line-through">
                      ${(product.user_price || 0).toFixed(2)}
                    </div>
                  )}
                  <div className="text-sm text-gray-400 mt-3">
                    {(product as any).has_digital_codes ? (
                      <span className="text-green-400">✓ أكواد رقمية متوفرة</span>
                    ) : (product as any).unlimited_stock ? (
                      <span className="text-green-400">✓ مخزون غير محدود</span>
                    ) : (product as any).track_inventory ? (
                      (product as any).manual_quantity && (product as any).manual_quantity > 0 ? (
                        <span className="text-green-400">✓ متوفر - {(product as any).manual_quantity} قطعة</span>
                      ) : (
                        <span className="text-red-400">✗ غير متوفر</span>
                      )
                    ) : (
                      <span className="text-green-400">✓ متوفر</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* PWP Layout - Original design for products with packages */
          <div>
            {/* Product Header */}
            <div className="text-center mb-8">
              <div className="text-sm text-purple-400 mb-2">{product.category?.name}</div>
              <h1 className="text-3xl font-bold text-white mb-4">{product.title}</h1>
              <p className="text-gray-300 leading-relaxed max-w-2xl mx-auto">{product.description}</p>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="max-w-4xl mx-auto pb-32">
          <div className="space-y-8">
            {/* Packages Grid (if available) */}
            {product.packages && product.packages.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-white mb-6 text-center">اختر الحزمة</h3>
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  {product.packages.map((pkg) => {
                    const effectivePkgPrice = getEffectivePrice(pkg)
                    const originalPkgPrice = pkg.user_price || 0
                    const pkgDiscount = hasDiscount(pkg)
                    const pkgDiscountPercentage = getDiscountPercentage(pkg)

                    return (
                      <div
                        key={pkg.id}
                        onClick={() => setSelectedPackage(pkg)}
                        className={`relative bg-gray-800/50 rounded-xl border cursor-pointer transition-all hover:scale-105 ${
                          selectedPackage?.id === pkg.id
                            ? 'border-purple-500 bg-purple-500/10 ring-2 ring-purple-500/20'
                            : 'border-gray-700 hover:border-gray-600'
                        }`}
                      >
                        {/* Discount Badge */}
                        {pkgDiscount && (
                          <div className="absolute top-3 right-3 z-10">
                            <div className="bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                              {pkgDiscountPercentage}%-
                            </div>
                          </div>
                        )}

                        {/* Package Image */}
                        <div className="relative aspect-video rounded-t-xl overflow-hidden bg-gray-700">
                          {pkg.image || product.cover_image ? (
                            <Image
                              src={pkg.image || product.cover_image || "/logo.jpg"}
                              alt={pkg.name}
                              fill
                              className="object-cover"
                              sizes="(max-width: 640px) 100vw, 50vw"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement
                                target.src = "/logo.jpg"
                              }}
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Package className="w-12 h-12 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Package Info */}
                        <div className="p-4 text-center">
                          <h4 className="font-semibold text-white mb-2 text-lg">{pkg.name}</h4>

                          {/* Price */}
                          <div className="mb-2">
                            <div className="text-2xl font-bold text-purple-400">
                              ${effectivePkgPrice.toFixed(2)}
                            </div>
                            {pkgDiscount && (
                              <div className="text-sm text-gray-500 line-through">
                                ${originalPkgPrice.toFixed(2)}
                              </div>
                            )}
                          </div>

                          {/* Description */}
                          {pkg.description && (
                            <p className="text-sm text-gray-400 mb-3">{pkg.description}</p>
                          )}

                          {/* Available indicator */}
                          <div className="text-xs font-medium">
                            {pkg.has_digital_codes ? (
                              <span className="text-green-400">✓ أكواد رقمية</span>
                            ) : pkg.unlimited_stock ? (
                              <span className="text-green-400">✓ مخزون غير محدود</span>
                            ) : pkg.track_inventory ? (
                              pkg.manual_quantity && pkg.manual_quantity > 0 ? (
                                <span className="text-green-400">✓ متوفر ({pkg.manual_quantity})</span>
                              ) : (
                                <span className="text-red-400">✗ غير متوفر</span>
                              )
                            ) : (
                              <span className="text-green-400">✓ متوفر</span>
                            )}
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}




          </div>
        </div>

        {/* Floating Purchase Flow */}
        <PurchaseFlow
          product={product as any}
          selectedPackage={selectedPackage}
          customFields={product.custom_fields || []}
          onPurchase={handlePurchase}
        />
      </div>
    </div>
  )
}
