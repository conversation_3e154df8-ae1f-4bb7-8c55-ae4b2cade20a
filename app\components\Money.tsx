'use client'

import { useState, useEffect } from 'react'

interface MoneyProps {
  usdAmount: number
  className?: string
  showOriginal?: boolean
}

export function Money({ usdAmount, className = '', showOriginal = false }: MoneyProps) {
  // Simple placeholders since DataContext was simplified
  const convertPrice = (price: number) => price
  const formatPrice = (price: number) => `$${price}`
  const selectedCurrency = 'USD'
  const [convertedAmount, setConvertedAmount] = useState(usdAmount)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const convert = async () => {
      try {
        setLoading(true)
        const converted = await convertPrice(usdAmount)
        setConvertedAmount(converted)
      } catch (error) {
        setConvertedAmount(usdAmount)
      } finally {
        setLoading(false)
      }
    }

    convert()
  }, [usdAmount, selectedCurrency, convertPrice])

  if (loading) {
    return <span className={`${className} animate-pulse`}>...</span>
  }

  return (
    <span className={className}>
      {formatPrice(convertedAmount)}
      {showOriginal && selectedCurrency !== 'USD' && (
        <span className="text-sm text-gray-500 ml-1">
          (${usdAmount.toFixed(2)})
        </span>
      )}
    </span>
  )
}
