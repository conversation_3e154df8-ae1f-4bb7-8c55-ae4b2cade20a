import { NextRequest, NextResponse } from 'next/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'
import { z } from 'zod'
import { getUserFromHeaders, requireAdmin, unauthorizedResponse, forbiddenResponse, getTenantId } from '../../../lib/auth-helpers'
import {
  productCreateSchema,
  productUpdateSchema,
  generateSlug,
  validatePricingRules
} from '../../../lib/products'


// GET /api/admin/products - Get products for current tenant
export async function GET(request: NextRequest): Promise<NextResponse> {
  console.log('🔍 Admin products API called')

  // Check authentication (set by middleware)
  const user = getUserFromHeaders(request)
  console.log('🔍 User from headers:', user ? `${user.email} (${user.role})` : 'null')

  if (!requireAdmin(user)) {
    console.log('🔍 Admin access denied')
    return user ? forbiddenResponse('Admin access required') : unauthorizedResponse()
  }

  try {
    const tenantId = getTenantId(request)

    // Create service client for admin operations
    const serviceClient = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Simple query - get all products for tenant
    const { data: products, error } = await serviceClient
      .from('products')
      .select(`
        id,
        tenant_id,
        title,
        slug,
        description,
        category_id,
        cover_image,
        tags,
        featured,
        original_price,
        user_price,
        discount_price,
        distributor_price,
        created_at,
        updated_at
      `)
      .eq('tenant_id', tenantId)
      .order('created_at', { ascending: false })
    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      data: products || []
    })

  } catch (error) {
    console.error('Error in products GET:', error)
    return NextResponse.json({ 
      success: false,
      error: 'حدث خطأ غير متوقع' 
    }, { status: 500 })
  }
}

// POST /api/admin/products - Create new product
export async function POST(request: NextRequest): Promise<NextResponse> {
  // Check authentication (set by middleware)
  const user = getUserFromHeaders(request)
  if (!requireAdmin(user)) {
    return user ? forbiddenResponse('Admin access required') : unauthorizedResponse()
  }

  try {
    const tenantId = getTenantId(request)
    const body = await request.json()

    // Validate input
    const validation = productCreateSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json({
        success: false,
        error: 'بيانات غير صالحة',
        details: validation.error.errors.map(e => e.message)
      }, { status: 400 })
    }

    // Create service client for admin operations
    const serviceClient = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    const validatedData = validation.data

    // Auto-generate slug if not provided
    if (!validatedData.slug) {
      validatedData.slug = generateSlug(validatedData.title)
    }

    // Create product
    const { data: newProduct, error } = await serviceClient
      .from('products')
      .insert({
        ...validatedData,
        tenant_id: tenantId
      })
      .select()
      .single()

    if (error) {
      throw error
    }

    return NextResponse.json({
      success: true,
      data: newProduct
    })
  } catch (error) {
    console.error('Error in products POST:', error)
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ غير متوقع'
    }, { status: 500 })
  }
}
