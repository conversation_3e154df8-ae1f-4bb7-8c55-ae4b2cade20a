import type React from "react"
import type { Metada<PERSON> } from "next"
import { Cairo } from "next/font/google"
import { headers } from "next/headers"
import "./globals.css"
import Header from "./components/Header"
import Footer from "./components/Footer"
import { DataProvider } from "./contexts/DataContext"
import { AuthProvider } from "./contexts/AuthContext"
import { TenantProvider } from "./contexts/TenantContext"
import TenantTheme from "./components/TenantTheme"
import AuthModal from "./components/auth/AuthModal"
import { ToastProvider } from "./components/Toast"
import { SkipToMain } from "./components/Accessibility"
import { Toaster } from "@/components/ui/sonner"
import { TenantResolver } from "./lib/tenant"

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  display: "swap",
})

// Generate dynamic metadata based on tenant
export async function generateMetadata(): Promise<Metadata> {
  const headersList = headers()
  const tenant = await TenantResolver.getTenantForSSR(headersList)

  const tenantName = tenant?.name || 'متجر بنتاكون'
  const title = `${tenantName} - خدمات الألعاب والمنتجات الرقمية`
  const description = `متجرك الموثوق لشحن الألعاب والحسابات والخدمات الرقمية - ${tenantName}`
  const logo = tenant?.theme_config?.logo || '/logo.jpg'

  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'),
    title,
    description,
    keywords: `ألعاب, شحن ألعاب, منتجات رقمية, خدمات ألعاب, ${tenantName}`,
    generator: 'v0.dev',
    icons: {
      icon: logo,
      shortcut: logo,
      apple: logo,
    },
    openGraph: {
      title,
      description,
      images: [logo],
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [logo],
    },
  }
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Get tenant data for SSR
  const headersList = headers()
  const tenant = await TenantResolver.getTenantForSSR(headersList)

  return (
    <html lang="ar" dir="rtl" className="dark">
      <body className={`${cairo.className} bg-gray-900 text-white min-h-screen`}>
        <SkipToMain />
        <TenantProvider initialTenant={tenant}>
          <TenantTheme>
            <AuthProvider>
              <DataProvider>
                <Header />
                <main id="main-content" className="min-h-screen" tabIndex={-1}>
                  {children}
                </main>
                <Footer />
                <AuthModal />
              </DataProvider>
            </AuthProvider>
          </TenantTheme>
        </TenantProvider>
        <ToastProvider />
        <Toaster />
      </body>
    </html>
  )
}
