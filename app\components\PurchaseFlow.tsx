"use client"

import { useState, useEffect } from 'react'
import { Plus, Minus, X, ChevronDown } from 'lucide-react'
import type { Product, Package as ProductPackage } from '../types'

interface CustomField {
  id: string
  label: string
  field_type: 'text' | 'dropdown' | 'email' | 'number'
  required: boolean
  placeholder?: string
  description?: string
  field_order: number
  validation_rules?: any
  display_options?: any
  options?: Array<{ value: string; label: string }>
}

interface PurchaseFlowProps {
  product: Product
  selectedPackage?: ProductPackage | null
  customFields: CustomField[]
  onPurchase: (data: {
    product: string
    package?: string
    customFields: Record<string, string>
    quantity: number
  }) => void
}

export default function PurchaseFlow({
  product,
  selectedPackage,
  customFields,
  onPurchase
}: PurchaseFlowProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [quantity, setQuantity] = useState(1)
  const [customFieldValues, setCustomFieldValues] = useState<Record<string, string>>({})
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  // Get current item (package or product)
  const currentItem = selectedPackage || product

  // Reset quantity to 1 when switching to a product that doesn't support quantity selection
  useEffect(() => {
    if (!shouldShowQuantitySelector(currentItem)) {
      setQuantity(1)
    }
  }, [currentItem])

  // Get effective price
  const getEffectivePrice = (item: Product | ProductPackage) => {
    if (item.discount_price) return item.discount_price
    return item.user_price || 0
  }

  // Check if has discount
  const hasDiscount = (item: Product | ProductPackage) => {
    return item.discount_price && item.user_price && item.discount_price < item.user_price
  }

  // Calculate discount percentage
  const getDiscountPercentage = (item: Product | ProductPackage) => {
    if (!hasDiscount(item)) return 0
    const original = item.user_price!
    const discounted = item.discount_price!
    return Math.round(((original - discounted) / original) * 100)
  }

  // Get inventory status
  const getInventoryStatus = (item: Product | ProductPackage) => {
    const anyItem = item as any
    if (anyItem.has_digital_codes) {
      return { text: "✓ أكواد رقمية متوفرة", color: "text-green-400" }
    } else if (anyItem.unlimited_stock) {
      return { text: "✓ مخزون غير محدود", color: "text-green-400" }
    } else if (anyItem.track_inventory) {
      if (anyItem.manual_quantity && anyItem.manual_quantity > 0) {
        return { text: `✓ متوفر - ${anyItem.manual_quantity} قطعة`, color: "text-green-400" }
      } else {
        return { text: "✗ غير متوفر", color: "text-red-400" }
      }
    } else {
      return { text: "✓ متوفر", color: "text-green-400" }
    }
  }

  // Check if quantity selector should be shown
  const shouldShowQuantitySelector = (item: Product | ProductPackage) => {
    // Show quantity selector for:
    // 1. Digital codes (each purchase consumes one code)
    // 2. Manual inventory tracking with available stock
    // 3. Unlimited stock
    // Hide for: No inventory tracking and not unlimited stock (single item only)
    const anyItem = item as any
    return anyItem.has_digital_codes ||
           anyItem.unlimited_stock ||
           (anyItem.track_inventory && anyItem.manual_quantity && anyItem.manual_quantity > 0)
  }

  // Handle quantity changes
  const increaseQuantity = () => {
    setQuantity(prev => prev + 1)
  }

  const decreaseQuantity = () => {
    setQuantity(prev => prev > 1 ? prev - 1 : 1)
  }

  // Handle custom field change
  const handleCustomFieldChange = (fieldId: string, value: string) => {
    setCustomFieldValues(prev => ({
      ...prev,
      [fieldId]: value
    }))
    
    // Clear validation error for this field
    if (validationErrors[fieldId]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[fieldId]
        return newErrors
      })
    }
  }

  // Validate custom fields
  const validateCustomFields = () => {
    const errors: Record<string, string> = {}
    
    customFields.forEach(field => {
      if (field.required && !customFieldValues[field.id]?.trim()) {
        errors[field.id] = `${field.label} مطلوب`
      }
    })
    
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Handle purchase
  const handlePurchase = () => {
    if (!isExpanded) {
      // First click - expand to show custom fields
      setIsExpanded(true)
      return
    }

    // Second click - validate and purchase
    if (!validateCustomFields()) {
      return
    }

    onPurchase({
      product: product.id,
      package: selectedPackage?.id,
      customFields: customFieldValues,
      quantity: quantity
    })
  }

  const effectivePrice = getEffectivePrice(currentItem)
  const originalPrice = currentItem.user_price || 0
  const discount = hasDiscount(currentItem)
  const discountPercentage = getDiscountPercentage(currentItem)
  const inventoryStatus = getInventoryStatus(currentItem)

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      {/* Backdrop for expanded state */}
      {isExpanded && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm"
          onClick={() => setIsExpanded(false)}
        />
      )}

      {/* Purchase Flow Container */}
      <div className={`relative bg-gray-900/95 backdrop-blur-md border-t border-gray-700 transition-all duration-300 ${
        isExpanded ? 'max-h-[80vh] overflow-y-auto' : 'max-h-32'
      }`}>
        
        {/* Collapsed State */}
        {!isExpanded && (
          <div className="p-4">
            <div className="max-w-md mx-auto">
              {/* Product Info Row */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-white truncate">
                    {selectedPackage?.name || product.title}
                  </h3>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-lg font-bold text-purple-400">
                      ${effectivePrice.toFixed(2)}
                    </span>
                    {discount && (
                      <span className="text-sm text-gray-500 line-through">
                        ${originalPrice.toFixed(2)}
                      </span>
                    )}
                    {discount && (
                      <span className="text-xs bg-red-500 text-white px-1.5 py-0.5 rounded">
                        {discountPercentage}%-
                      </span>
                    )}
                  </div>
                </div>

                {/* Quantity Selector - Only show if product supports quantity selection */}
                {shouldShowQuantitySelector(currentItem) && (
                  <div className="flex items-center gap-2 mr-3">
                    <button
                      className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center justify-center text-white transition-colors"
                      onClick={decreaseQuantity}
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <div className="bg-gray-700 px-3 py-1 rounded-lg min-w-[40px] text-center">
                      <span className="text-white font-medium text-sm">{quantity}</span>
                    </div>
                    <button
                      className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center justify-center text-white transition-colors"
                      onClick={increaseQuantity}
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>

              {/* Status and Purchase Button Row */}
              <div className="flex items-center justify-between">
                <div className={`text-xs ${inventoryStatus.color}`}>
                  {inventoryStatus.text}
                </div>
                <button
                  onClick={handlePurchase}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-2 px-6 rounded-lg transition-all duration-300 text-sm"
                >
                  شحن الآن
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Expanded State */}
        {isExpanded && (
          <div className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">معلومات المنتج</h2>
              <button
                onClick={() => setIsExpanded(false)}
                className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center justify-center text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Product Summary */}
            <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
              <h3 className="font-semibold text-white mb-2">
                {selectedPackage?.name || product.title}
              </h3>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold text-purple-400">
                    ${effectivePrice.toFixed(2)}
                  </span>
                  {discount && (
                    <span className="text-sm text-gray-500 line-through">
                      ${originalPrice.toFixed(2)}
                    </span>
                  )}
                </div>
                <div className={`text-sm ${inventoryStatus.color}`}>
                  {inventoryStatus.text}
                </div>
              </div>
            </div>

            {/* Custom Fields */}
            {customFields && customFields.length > 0 && (
              <div className="space-y-4 mb-6">
                {customFields.map((field) => (
                  <div key={field.id}>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {field.label}
                      {field.required && <span className="text-red-400 mr-1">*</span>}
                    </label>
                    
                    {field.field_type === 'dropdown' ? (
                      <div className="relative">
                        <select
                          value={customFieldValues[field.id] || ''}
                          onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                          className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white focus:outline-none focus:border-purple-500 transition-colors appearance-none ${
                            validationErrors[field.id] ? 'border-red-500' : 'border-gray-700'
                          }`}
                        >
                          <option value="">اختر...</option>
                          {field.options?.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                      </div>
                    ) : (
                      <input
                        type={field.field_type === 'email' ? 'email' : field.field_type === 'number' ? 'number' : 'text'}
                        placeholder={field.placeholder}
                        value={customFieldValues[field.id] || ''}
                        onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                        className={`w-full px-3 py-2 bg-gray-800 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors ${
                          validationErrors[field.id] ? 'border-red-500' : 'border-gray-700'
                        }`}
                      />
                    )}
                    
                    {validationErrors[field.id] && (
                      <p className="text-red-400 text-sm mt-1">{validationErrors[field.id]}</p>
                    )}
                    
                    {field.description && (
                      <p className="text-gray-500 text-sm mt-1">{field.description}</p>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Quantity and Total - Only show if product supports quantity selection */}
            {shouldShowQuantitySelector(currentItem) ? (
              <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-gray-300">الكمية</span>
                  <div className="flex items-center gap-2">
                    <button
                      className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center justify-center text-white transition-colors"
                      onClick={decreaseQuantity}
                    >
                      <Minus className="w-4 h-4" />
                    </button>
                    <div className="bg-gray-700 px-4 py-2 rounded-lg">
                      <span className="text-white font-medium">{quantity}</span>
                    </div>
                    <button
                      className="w-8 h-8 bg-gray-700 hover:bg-gray-600 rounded-lg flex items-center justify-center text-white transition-colors"
                      onClick={increaseQuantity}
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="flex items-center justify-between text-lg font-bold">
                  <span className="text-white">المجموع</span>
                  <span className="text-purple-400">
                    ${(effectivePrice * quantity).toFixed(2)}
                  </span>
                </div>
              </div>
            ) : (
              /* Single item - just show total price */
              <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between text-lg font-bold">
                  <span className="text-white">السعر</span>
                  <span className="text-purple-400">
                    ${effectivePrice.toFixed(2)}
                  </span>
                </div>
              </div>
            )}

            {/* Purchase Button */}
            <button
              onClick={handlePurchase}
              className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 text-lg"
            >
              تأكيد الطلب
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
