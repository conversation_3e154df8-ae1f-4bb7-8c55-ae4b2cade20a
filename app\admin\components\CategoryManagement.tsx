"use client"

import React, { useState, useEffect } from 'react'
import { Plus, Tag, Save } from 'lucide-react'
import { useAdminCRUD } from '../hooks/useAdminCRUD'
import { useAdminModal } from '../hooks/useAdminModal'
import AdminTable, { ColumnDefinition } from './shared/AdminTable'
import AdminPagination from './shared/AdminPagination'
import AdminFilters, { FilterOption } from './shared/AdminFilters'
import AdminModal from './shared/AdminModal'
import { useTenant } from '../../contexts/TenantContext'
import {
  createCategory,
  updateCategory,
  deleteCategory,
  generateSlug
} from '../../lib/categories'

interface Category {
  id: string
  name: string
  slug: string
  description?: string
  image: string
  is_active?: boolean
  product_count?: number
  created_at: string
  updated_at: string
}

export default function CategoryManagement() {
  const { tenant } = useTenant()
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    image: ''
  })

  // CRUD operations using the categories API
  const {
    items: categories,
    loading,
    actionLoading,
    pagination,
    filters,
    fetchItems,
    goToPage,
    nextPage,
    prevPage,
    updateFilters
  } = useAdminCRUD<Category>({
    entityType: 'categories',
    apiEndpoint: '/api/categories'
  })

  // Modal management
  const {
    isOpen: isModalOpen,
    mode: modalMode,
    item: editingCategory,
    openCreateModal,
    openEditModal,
    closeModal
  } = useAdminModal<Category>()

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isModalOpen) {
      if (modalMode === 'edit' && editingCategory) {
        setFormData({
          name: editingCategory.name || '',
          slug: editingCategory.slug,
          description: editingCategory.description || '',
          image: editingCategory.image
        })
      } else {
        setFormData({
          name: '',
          slug: '',
          description: '',
          image: ''
        })
      }
    }
  }, [isModalOpen, modalMode, editingCategory])

  // Table columns definition
  const columns: ColumnDefinition<Category>[] = [
    {
      key: 'name',
      label: 'الفئة',
      render: (category) => (
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-600/50">
            <img
              src={category.image}
              alt={category.name || category.slug}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = "/logo.jpg"
              }}
            />
          </div>
          <div>
            <h3 className="font-semibold">{category.name || category.slug}</h3>
            <p className="text-sm text-gray-400">{category.slug}</p>
          </div>
        </div>
      )
    },
    {
      key: 'description',
      label: 'الوصف',
      render: (category) => (
        <span className="text-sm">{category.description || 'لا يوجد وصف'}</span>
      )
    },
    {
      key: 'product_count',
      label: 'المنتجات',
      render: (category) => `${category.product_count || 0} منتج`
    },
    {
      key: 'created_at',
      label: 'تاريخ الإنشاء',
      render: (category) => new Date(category.created_at).toLocaleDateString('ar-SA')
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'has_products',
      label: 'تحتوي على منتجات',
      type: 'boolean'
    }
  ]

  // Mobile card renderer
  const renderMobileCard = (category: Category) => (
    <div>
      <div className="flex items-center space-x-4 space-x-reverse mb-3">
        <div className="w-16 h-16 rounded-xl overflow-hidden bg-gray-600/50">
          <img
            src={category.image}
            alt={category.name || category.slug}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.src = "/logo.jpg"
            }}
          />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-lg truncate">
            {category.name || category.slug}
          </h3>
          <p className="text-sm text-gray-400 truncate">
            {category.description || 'لا يوجد وصف'}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-xs text-gray-400 mb-1">الرابط</p>
          <p className="text-sm font-medium">{category.slug}</p>
        </div>
        <div>
          <p className="text-xs text-gray-400 mb-1">المنتجات</p>
          <p className="text-sm font-medium">{category.product_count || 0}</p>
        </div>
      </div>
    </div>
  )

  // Handle name change with auto-slug generation
  const handleNameChange = (name: string) => {
    setFormData(prev => ({
      ...prev,
      name,
      slug: prev.slug || generateSlug(name)
    }))
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!tenant) return

    if (!formData.slug.trim() || !formData.image.trim()) {
      alert('يرجى ملء الحقول المطلوبة (الرابط المختصر والصورة)')
      return
    }

    try {
      let result
      if (modalMode === 'edit' && editingCategory) {
        result = await updateCategory(editingCategory.id, formData, tenant.id)
      } else {
        result = await createCategory(formData, tenant.id)
      }

      if (result.success) {
        closeModal()
        // Refresh the list
        fetchItems(pagination.page, filters, false)
      } else {
        alert(result.error || 'حدث خطأ')
      }
    } catch (error) {
      console.error('Category save error:', error)
      alert('حدث خطأ غير متوقع')
    }
  }

  // Handle delete
  const handleDelete = async (category: Category) => {
    if (!tenant) return
    
    if ((category.product_count || 0) > 0) {
      alert('لا يمكن حذف فئة تحتوي على منتجات')
      return
    }

    if (!confirm('هل أنت متأكد من حذف هذه الفئة؟')) return

    try {
      const result = await deleteCategory(category.id, tenant.id)
      if (result.success) {
        // Refresh the list
        fetchItems(pagination.page, filters, false)
      } else {
        alert(result.error || 'فشل في حذف الفئة')
      }
    } catch (error) {
      console.error('Category delete error:', error)
      alert('حدث خطأ غير متوقع')
    }
  }

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl md:text-2xl font-bold">إدارة الفئات</h2>
        <button 
          onClick={openCreateModal}
          className="btn-primary flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          إضافة فئة
        </button>
      </div>

      {/* Filters */}
      <AdminFilters
        filters={filters}
        onFiltersChange={updateFilters}
        filterOptions={filterOptions}
        searchPlaceholder="البحث في الفئات..."
      />

      {/* Table */}
      <AdminTable
        items={categories}
        columns={columns}
        loading={loading}
        actionLoading={actionLoading}
        onEdit={openEditModal}
        onDelete={handleDelete}
        renderMobileCard={renderMobileCard}
        emptyState={{
          icon: <Tag className="w-16 h-16 text-gray-400 mx-auto" />,
          title: 'لا توجد فئات',
          description: 'ابدأ بإضافة فئة جديدة لتنظيم منتجاتك',
          action: (
            <button onClick={openCreateModal} className="btn-primary">
              إضافة فئة جديدة
            </button>
          )
        }}
      />

      {/* Pagination */}
      <AdminPagination
        pagination={pagination}
        onPageChange={goToPage}
        onNextPage={nextPage}
        onPrevPage={prevPage}
        entityName="فئة"
      />

      {/* Category Modal */}
      <AdminModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalMode === 'edit' ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
        size="lg"
      >
        <div className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">اسم الفئة</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleNameChange(e.target.value)}
                className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                placeholder="أدخل اسم الفئة (اختياري)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">الرابط المختصر *</label>
              <input
                type="text"
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
                placeholder="category-name-here"
                required
              />
              <p className="text-xs text-gray-400 mt-1">
                يجب أن يكون بصيغة: name-name-name (أحرف صغيرة وشرطات فقط)
              </p>
            </div>
          </div>

          {/* Image */}
          <div>
            <label className="block text-sm font-medium mb-2">صورة الفئة *</label>
            <input
              type="url"
              value={formData.image}
              onChange={(e) => setFormData(prev => ({ ...prev, image: e.target.value }))}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
              placeholder="https://example.com/image.jpg"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium mb-2">الوصف</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full bg-gray-700/50 border border-gray-600/50 rounded-lg px-3 py-2 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20"
              placeholder="وصف اختياري للفئة"
            />
          </div>

          {/* Preview */}
          {(formData.image || formData.name || formData.slug) && (
            <div>
              <label className="block text-sm font-medium mb-2">معاينة</label>
              <div className="bg-gray-700/30 rounded-xl p-4 border border-gray-600/50">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="w-16 h-16 rounded-xl overflow-hidden bg-gray-600/50">
                    <img
                      src={formData.image || "/logo.jpg"}
                      alt="Category preview"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement
                        target.src = "/logo.jpg"
                      }}
                    />
                  </div>
                  <div>
                    <h4 className="font-semibold">
                      {formData.name || formData.slug || 'اسم الفئة'}
                    </h4>
                    <p className="text-sm text-gray-400">
                      {formData.description || 'وصف الفئة'}
                    </p>
                    <p className="text-xs text-gray-500">
                      الرابط: {formData.slug || 'category-slug'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-4 space-x-reverse pt-6 border-t border-gray-700/50">
            <button
              onClick={handleSubmit}
              disabled={!formData.slug.trim() || !formData.image.trim()}
              className="flex-1 btn-primary flex items-center justify-center gap-2"
            >
              <Save className="w-4 h-4" />
              {modalMode === 'edit' ? 'تحديث' : 'إضافة'}
            </button>
            <button 
              onClick={closeModal}
              className="flex-1 btn-secondary"
            >
              إلغاء
            </button>
          </div>
        </div>
      </AdminModal>
    </div>
  )
}
