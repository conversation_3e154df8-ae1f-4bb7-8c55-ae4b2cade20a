import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '../../../lib/supabase/server'

// GET /api/products/[slug] - Get single product by slug (public endpoint)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
): Promise<NextResponse> {
  try {
    const { slug } = await params

    // Get tenant from headers (same pattern as other API routes)
    let tenantId = request.headers.get('x-tenant-id')

    const supabase = await createClient()

    // If no tenant in headers, try to get from user profile or use fallback
    if (!tenantId) {
      try {
        const { data: { user } } = await supabase.auth.getUser()

        if (user) {
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('tenant_id')
            .eq('id', user.id)
            .single()

          tenantId = profile?.tenant_id
        }
      } catch (authError) {
        // No authenticated user, will use main tenant fallback
      }
    }

    // Fallback to main tenant if no tenant found
    if (!tenantId) {
      const { data: mainTenant } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', 'main')
        .single()

      tenantId = mainTenant?.id || process.env.DEFAULT_TENANT_ID || 'default-tenant'
    }



    // Get product with related data (public data only)
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        id,
        title,
        slug,
        description,
        category_id,
        cover_image,
        tags,
        featured,
        original_price,
        user_price,
        distributor_price,
        discount_price,
        manual_quantity,
        track_inventory,
        unlimited_stock,
        has_digital_codes,
        created_at,
        updated_at
      `)
      .eq('slug', slug)
      .eq('tenant_id', tenantId)
      .single()



    if (error || !product) {
      return NextResponse.json({
        success: false,
        error: 'المنتج غير موجود'
      }, { status: 404 })
    }

    // Get category data
    let category = null
    if (product.category_id) {
      const { data: categoryData } = await supabase
        .from('categories')
        .select('id, name, slug')
        .eq('id', product.category_id)
        .single()
      category = categoryData
    }

    // Get packages
    const { data: packages } = await supabase
      .from('packages')
      .select(`
        id,
        name,
        description,
        price,
        original_price,
        user_price,
        distributor_price,
        discount_price,
        image,
        manual_quantity,
        track_inventory,
        unlimited_stock,
        has_digital_codes,
        created_at,
        updated_at
      `)
      .eq('product_id', product.id)
      .order('created_at', { ascending: true })

    // Get custom fields
    const { data: customFields } = await supabase
      .from('custom_fields')
      .select(`
        id,
        label,
        field_type,
        required,
        placeholder,
        description,
        field_order,
        validation_rules,
        display_options
      `)
      .eq('product_id', product.id)
      .order('field_order', { ascending: true })

    // Combine all data
    const productWithDetails = {
      ...product,
      category,
      packages: packages || [],
      custom_fields: customFields || []
    }

    return NextResponse.json({
      success: true,
      data: productWithDetails
    })

  } catch (error) {
    console.error('Error in product GET:', error)
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ غير متوقع'
    }, { status: 500 })
  }
}
