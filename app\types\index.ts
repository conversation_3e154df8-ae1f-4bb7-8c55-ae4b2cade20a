/**
 * Category Entity - Represents product categories with tenant isolation
 *
 * Database Table: categories
 * Primary Key: id
 * Indexes: tenant_id, slug, is_active, sort_order
 *
 * Relationships:
 * - Many-to-One with Tenant (tenant_id)
 * - One-to-Many with Product (category_id)
 */
export interface Category {
  id: string              // UUID - Primary key, auto-generated
  tenant_id: string       // UUID - Tenant identifier for multi-tenant support
  name?: string           // Optional display name of the category
  slug: string            // URL-friendly identifier, unique per tenant (required)
  description?: string    // Optional description for the category
  image: string           // Image URL for the category (required)
  created_at: string      // ISO timestamp of creation
  updated_at: string      // ISO timestamp of last update
  product_count?: number  // Virtual field - number of products in category
}

/**
 * Product Entity - Represents products with tenant isolation
 *
 * Database Table: products
 * Primary Key: id
 * Indexes: tenant_id, category_id, slug, featured
 *
 * Relationships:
 * - Many-to-One with Category (category_id)
 * - Many-to-One with Tenant (tenant_id)
 * - One-to-Many with Package (product_id)
 * - One-to-Many with CustomField (product_id)
 */
export interface Product {
  id: string                    // UUID - Primary key, auto-generated
  tenant_id: string            // UUID - Tenant identifier for multi-tenant support
  title: string                // Product title (required)
  slug: string                 // URL-friendly identifier, unique per tenant
  description: string          // Product description (required)
  category_id: string          // UUID - Reference to categories table (required)
  cover_image?: string         // Image URL for the product (optional)
  tags?: string[]              // Array of tag strings (optional)
  featured: boolean            // Whether product is featured (optional, default false)

  // Pricing fields (only used when no packages exist)
  original_price?: number      // Base cost price
  user_price?: number          // Regular customer price
  discount_price?: number      // Discounted price (optional)
  distributor_price?: number   // Distributor/reseller price (optional)

  created_at: string           // ISO timestamp of creation
  updated_at: string           // ISO timestamp of last update

  // Virtual fields for UI display
  packages?: Package[]         // Related packages
  custom_fields?: CustomField[] // Related custom fields
  category?: {                 // Related category info
    id: string
    name: string
    slug: string
  }
}

/**
 * Package Entity - Represents product packages/variants with pricing
 *
 * Database Table: packages
 * Primary Key: id
 * Indexes: tenant_id, product_id
 *
 * Relationships:
 * - Many-to-One with Product (product_id)
 * - Many-to-One with Tenant (tenant_id)
 * - One-to-Many with DigitalCode (package_id)
 */
export interface Package {
  id: string                   // UUID - Primary key, auto-generated
  tenant_id: string           // UUID - Tenant identifier for multi-tenant support
  product_id: string          // UUID - Reference to products table (required)
  name: string                // Package name (required)
  description?: string        // Package description (optional)

  // Pricing fields (required)
  original_price: number      // Base cost price (required)
  user_price: number          // Regular customer price (required)
  discount_price?: number     // Discounted price (optional)
  distributor_price?: number  // Distributor/reseller price (optional)

  // Digital content
  digital_codes?: string      // Digital codes, one per line (optional)
  image?: string              // Package image URL (optional, fallback to product image)

  // Inventory fields (from migration)
  manual_quantity?: number    // Manual inventory quantity
  track_inventory?: boolean   // Whether to track inventory
  unlimited_stock?: boolean   // Whether package has unlimited stock
  has_digital_codes?: boolean // Whether package uses digital codes

  created_at: string          // ISO timestamp of creation
  updated_at: string          // ISO timestamp of last update
}

/**
 * DigitalCode Entity - Represents digital codes for packages
 *
 * Database Table: digital_codes
 * Primary Key: id
 * Indexes: tenant_id, package_id, assigned_to_order_id
 *
 * Relationships:
 * - Many-to-One with Package (package_id)
 * - Many-to-One with Tenant (tenant_id)
 * - Many-to-One with Order (assigned_to_order_id)
 */
export interface DigitalCode {
  id: string                  // UUID - Primary key, auto-generated
  tenant_id: string          // UUID - Tenant identifier for multi-tenant support
  package_id: string         // UUID - Reference to packages table
  key_encrypted: string      // Encrypted digital code/key
  used: boolean              // Whether code has been used
  assigned_to_order_id?: string // UUID - Order this code is assigned to
  assigned_at?: string       // ISO timestamp when assigned
  viewed_count: number       // Number of times code was viewed
  last_viewed_at?: string    // ISO timestamp of last view
  created_at: string         // ISO timestamp of creation
  updated_at: string         // ISO timestamp of last update
}

/**
 * CustomField Entity - Represents dynamic form fields for products
 *
 * Database Table: custom_fields
 * Primary Key: id
 * Indexes: tenant_id, product_id, field_order
 *
 * Relationships:
 * - Many-to-One with Product (product_id)
 * - Many-to-One with Tenant (tenant_id)
 */
export interface CustomField {
  id: string                  // UUID - Primary key, auto-generated
  tenant_id: string          // UUID - Tenant identifier for multi-tenant support
  product_id: string         // UUID - Reference to products table
  label: string              // Field label (required)
  field_type: 'text' | 'dropdown' // Field type (required)
  required: boolean          // Whether field is required
  placeholder?: string       // Placeholder text for text fields
  description?: string       // Field description/help text
  field_order: number        // Display order (required)

  // For dropdown fields
  options?: DropdownOption[] // Dropdown options

  created_at: string         // ISO timestamp of creation
  updated_at: string         // ISO timestamp of last update
}

/**
 * DropdownOption Entity - Represents options for dropdown custom fields
 */
export interface DropdownOption {
  label: string              // Display label (required)
  value: string              // Option value (required)
}

/**
 * User Entity - Represents system users with role-based access
 *
 * Database Table: users (managed by Supabase Auth)
 * Primary Key: id (UUID from Supabase Auth)
 * Indexes: email (unique), role
 *
 * Relationships:
 * - One-to-Many with Order (userId)
 *
 * Security: Integrated with Supabase Auth for authentication
 * Row Level Security (RLS) policies required for data protection
 */
export interface User {
  id: string                                    // UUID from Supabase Auth
  tenant_id?: string                            // UUID - Tenant identifier for multi-tenant support
  email: string                                 // User email (unique, from Auth)
  name: string                                  // Display name
  role: "admin" | "distributor" | "user" | "worker"  // User role for access control
  walletBalance: number                         // User's wallet balance in USD
  avatar?: string                               // Optional profile picture URL
  phone?: string                                // Optional phone number
  createdAt?: string                            // Account creation date
}

/**
 * Order Entity - Represents customer purchase transactions
 *
 * Database Table: orders
 * Primary Key: id
 * Foreign Keys:
 *   - user_id (references user_profiles.id)
 *   - product_id (references products.id)
 *   - package_id (references packages.id)
 *   - tenant_id (references tenants.id)
 * Indexes: (tenant_id, user_id), (tenant_id, product_id), (tenant_id, status), (tenant_id, created_at)
 *
 * Relationships:
 * - Many-to-One with User (user_id)
 * - Many-to-One with Product (product_id)
 * - Many-to-One with Package (package_id)
 * - Many-to-One with Tenant (tenant_id)
 * - One-to-Many with DigitalCode (via assigned_to_order_id)
 *
 * Security: Users can only access their own orders within their tenant (RLS policy)
 */
export interface Order {
  id: string                                    // UUID - Primary key
  tenant_id: string                             // UUID - Tenant identifier for multi-tenant support
  user_id: string                               // Customer who placed the order
  product_id: string                            // Product being purchased
  package_id: string                            // Specific package/variant
  amount: number                                // Total amount paid in USD
  status: "pending" | "completed" | "failed"   // Order processing status
  custom_data?: Record<string, any>             // Custom field data (Player ID, etc.)
  created_at: string                            // ISO timestamp of order creation
  updated_at: string                            // ISO timestamp of last update

  // Worker tracking fields
  worker_id?: string                            // UUID - Worker who processed the order
  worker_action?: "accepted" | "rejected"       // Action taken by worker
  worker_action_at?: string                     // ISO timestamp of worker action

  // Joined data from related tables (when using SELECT with joins)
  products?: {
    id: string
    title: string
    slug: string
    coverImage: string
  }
  packages?: {
    id: string
    name: string
    price: number
    image: string
  }
  user_profiles?: {
    id: string
    name: string
    email: string
  }
  worker_profiles?: {
    id: string
    name: string
    email: string
    role: string
  }
  digitalCodes?: {
    id: string
    key_encrypted: string
    used: boolean
    viewed_count: number
    last_viewed_at: string
  }[]
}

/**
 * BannerSlide Entity - Represents promotional banners on homepage
 *
 * Database Table: banner_slides
 * Primary Key: id
 * Indexes: active, order
 *
 * Used for homepage carousel/banner management
 */
export interface BannerSlide {
  id: string                                          // UUID - Primary key
  tenant_id?: string                                  // UUID - Tenant identifier for multi-tenant support
  title: string                                       // Banner title text
  subtitle?: string                                   // Optional subtitle text
  image: string                                       // Banner image URL
  linkType: "product" | "collection" | "custom" | "none"  // Type of link action
  linkValue?: string                                  // Link target (product slug, URL, etc.)
  active: boolean                                     // Whether banner is currently shown
  order: number                                       // Display order (lower = first)
}

/**
 * HomepageSection Entity - Represents product sections on homepage
 *
 * Database Table: homepage_sections
 * Primary Key: id
 * Indexes: active, order
 *
 * Relationships:
 * - References Product (productIds array)
 *
 * Used for organizing products into themed sections
 */
export interface HomepageSection {
  id: string              // UUID - Primary key
  tenant_id?: string      // UUID - Tenant identifier for multi-tenant support
  title: string           // Section title (e.g., "🔥 Popular Games") - can include emojis directly
  productIds: string[]    // Array of product IDs to display
  order: number           // Display order on homepage
  active: boolean         // Whether section is currently shown
}

/**
 * HomepageConfig - Configuration object for homepage layout
 *
 * Not a database table - used for API responses and admin management
 */
export interface HomepageConfig {
  banners: BannerSlide[]      // Active banner slides
  sections: HomepageSection[] // Active homepage sections
}
